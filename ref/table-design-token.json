{"name": "Schedule Dashboard Component Design Tokens", "version": "1.0.0", "description": "Design tokens specifically for the Schedule Dashboard table and tabs components", "extends": "ref/design-token-base.json", "components": {"scheduleTabs": {"description": "Press selection tabs for schedule dashboard", "container": {"background": {"value": "#f1f5f9", "description": "Tab container background", "tailwind": "bg-slate-100"}, "height": {"value": "48px", "description": "Tab container height", "tailwind": "h-12"}, "borderRadius": {"value": "8px", "description": "Tab container border radius", "tailwind": "rounded-md"}, "padding": {"value": "4px", "description": "Internal padding", "tailwind": "p-1"}}, "tab": {"default": {"background": {"value": "transparent", "description": "Default tab background", "tailwind": "bg-transparent"}, "color": {"value": "#64748b", "description": "Default tab text color", "tailwind": "text-slate-500"}, "padding": {"horizontal": "24px", "vertical": "8px", "description": "Tab padding", "tailwind": "px-6 py-2"}, "fontSize": {"value": "16px", "description": "Tab font size", "tailwind": "text-md"}, "fontWeight": {"value": "500", "description": "Tab font weight", "tailwind": "font-medium"}, "borderRadius": {"value": "6px", "description": "Individual tab border radius", "tailwind": "rounded-md"}, "transition": {"value": "all 150ms ease-in-out", "description": "Tab transition", "tailwind": "transition-all duration-150"}}, "active": {"background": {"value": "#00B2A1", "description": "Active tab background", "tailwind": "bg-[#00B2A1]"}, "color": {"value": "#ffffff", "description": "Active tab text color", "tailwind": "text-white"}, "fontWeight": {"value": "700", "description": "Active tab font weight", "tailwind": "font-bold"}, "shadow": {"value": "0 2px 4px rgba(0, 178, 161, 0.2)", "description": "Active tab shadow", "tailwind": "shadow-md shadow-[#00B2A1]/20"}}, "hover": {"background": {"value": "rgba(0, 178, 161, 0.1)", "description": "Tab hover background", "tailwind": "hover:bg-[#00B2A1]/10"}, "color": {"value": "#00B2A1", "description": "Tab hover text color", "tailwind": "hover:text-[#00B2A1]"}}}}, "scheduleTable": {"description": "Hierarchical schedule table with expandable rows", "container": {"background": {"value": "#ffffff", "description": "Table container background", "tailwind": "bg-white"}, "borderRadius": {"value": "12px", "description": "Table container border radius", "tailwind": "rounded-xl"}, "border": {"value": "1px solid #e2e8f0", "description": "Table container border", "tailwind": "border border-slate-200"}, "shadow": {"value": "0 1px 3px rgba(0, 0, 0, 0.1)", "description": "Table container shadow", "tailwind": "shadow-sm"}, "maxHeight": {"value": "calc(100vh - 230px)", "description": "Maximum table height for scrolling", "tailwind": "max-h-[calc(100vh-230px)]"}, "overflow": {"value": "auto", "description": "Table overflow behavior", "tailwind": "overflow-y-auto"}}, "header": {"background": {"value": "#ffffff", "description": "Table header background", "tailwind": "bg-white"}, "position": {"value": "sticky", "description": "Sticky header positioning", "tailwind": "sticky top-0"}, "zIndex": {"value": "10", "description": "Header z-index for stacking", "tailwind": "z-10"}, "borderBottom": {"value": "1px solid #e2e8f0", "description": "Header bottom border", "tailwind": "border-b border-slate-200"}, "cell": {"padding": {"value": "12px", "description": "Header cell padding", "tailwind": "p-3"}, "textAlign": {"value": "center", "description": "Header text alignment", "tailwind": "text-center"}, "fontWeight": {"value": "600", "description": "Header font weight", "tailwind": "font-semibold"}, "fontSize": {"value": "14px", "description": "Header font size", "tailwind": "text-sm"}, "color": {"value": "#374151", "description": "Header text color", "tailwind": "text-gray-700"}, "whiteSpace": {"value": "nowrap", "description": "Prevent header text wrapping", "tailwind": "whitespace-nowrap"}}}, "rows": {"recipeHeader": {"background": {"value": "#eff6ff", "description": "Recipe header background", "tailwind": "bg-blue-50"}, "borderBottom": {"value": "1px solid #bfdbfe", "description": "Recipe header border", "tailwind": "border-b border-blue-200"}, "cursor": {"value": "pointer", "description": "Recipe header cursor", "tailwind": "cursor-pointer"}, "hover": {"background": {"value": "#dbeafe", "description": "Recipe header hover background", "tailwind": "hover:bg-blue-100"}}, "padding": {"value": "12px", "description": "Recipe header padding", "tailwind": "p-3"}, "title": {"color": {"value": "#1e40af", "description": "Recipe title color", "tailwind": "text-blue-800"}, "fontWeight": {"value": "600", "description": "Recipe title font weight", "tailwind": "font-semibold"}, "fontSize": {"value": "16px", "description": "Recipe title font size", "tailwind": "text-base"}}, "subtitle": {"color": {"value": "#2563eb", "description": "Recipe subtitle color", "tailwind": "text-blue-600"}, "fontSize": {"value": "14px", "description": "Recipe subtitle font size", "tailwind": "text-sm"}}, "badge": {"background": {"value": "#dbeafe", "description": "Recipe badge background", "tailwind": "bg-blue-100"}, "color": {"value": "#1d4ed8", "description": "Recipe badge text color", "tailwind": "text-blue-700"}, "padding": {"horizontal": "8px", "vertical": "4px", "description": "Recipe badge padding", "tailwind": "px-2 py-1"}, "borderRadius": {"value": "9999px", "description": "Recipe badge border radius", "tailwind": "rounded-full"}, "fontSize": {"value": "14px", "description": "Recipe badge font size", "tailwind": "text-sm"}, "fontWeight": {"value": "500", "description": "Recipe badge font weight", "tailwind": "font-medium"}}, "expandIcon": {"size": {"value": "20px", "description": "Recipe expand icon size", "tailwind": "w-5 h-5"}, "color": {"value": "#1d4ed8", "description": "Recipe expand icon color", "tailwind": "text-blue-700"}, "margin": {"right": "8px", "description": "Recipe expand icon margin", "tailwind": "mr-2"}}}, "scheduleRow": {"background": {"value": "#f8fafc", "description": "Schedule row background", "tailwind": "bg-gray-100"}, "borderBottom": {"value": "1px solid #e2e8f0", "description": "Schedule row border", "tailwind": "border-b border-slate-200"}, "cursor": {"value": "pointer", "description": "Schedule row cursor", "tailwind": "cursor-pointer"}, "hover": {"background": {"value": "#f1f5f9", "description": "Schedule row hover background", "tailwind": "hover:bg-gray-200"}}, "padding": {"value": "12px", "description": "Schedule row padding", "tailwind": "p-3"}, "expandIcon": {"size": {"value": "16px", "description": "Schedule expand icon size", "tailwind": "w-4 h-4"}, "color": {"value": "#6b7280", "description": "Schedule expand icon color", "tailwind": "text-gray-500"}}, "pressBadge": {"background": {"value": "#dbeafe", "description": "Press badge background", "tailwind": "bg-blue-100"}, "color": {"value": "#1e40af", "description": "Press badge text color", "tailwind": "text-blue-800"}, "padding": {"horizontal": "8px", "vertical": "4px", "description": "Press badge padding", "tailwind": "px-2 py-1"}, "borderRadius": {"value": "9999px", "description": "Press badge border radius", "tailwind": "rounded-full"}, "fontSize": {"value": "14px", "description": "Press badge font size", "tailwind": "text-sm"}, "fontWeight": {"value": "500", "description": "Press badge font weight", "tailwind": "font-medium"}}, "weightBadge": {"background": {"value": "#dbeafe", "description": "Weight badge background", "tailwind": "bg-blue-100"}, "color": {"value": "#1d4ed8", "description": "Weight badge text color", "tailwind": "text-blue-700"}, "padding": {"horizontal": "8px", "vertical": "4px", "description": "Weight badge padding", "tailwind": "px-2 py-1"}, "borderRadius": {"value": "9999px", "description": "Weight badge border radius", "tailwind": "rounded-full"}, "fontSize": {"value": "14px", "description": "Weight badge font size", "tailwind": "text-sm"}, "fontWeight": {"value": "500", "description": "Weight badge font weight", "tailwind": "font-medium"}}}, "soItemRow": {"background": {"value": "#ffffff", "description": "SO item row background", "tailwind": "bg-white"}, "borderBottom": {"value": "1px solid #f1f5f9", "description": "SO item row border", "tailwind": "border-b border-slate-100"}, "hover": {"background": {"value": "#f9fafb", "description": "SO item row hover background", "tailwind": "hover:bg-gray-50"}}, "fontSize": {"value": "14px", "description": "SO item row font size", "tailwind": "text-sm"}, "cell": {"padding": {"value": "12px", "description": "SO item cell padding", "tailwind": "p-3"}, "textAlign": {"value": "center", "description": "SO item cell text alignment", "tailwind": "text-center"}, "whiteSpace": {"value": "nowrap", "description": "Prevent SO item text wrapping", "tailwind": "whitespace-nowrap"}, "overflow": {"value": "hidden", "description": "SO item cell overflow", "tailwind": "overflow-hidden"}, "textOverflow": {"value": "ellipsis", "description": "SO item cell text overflow", "tailwind": "text-ellipsis"}}, "indentedCell": {"paddingLeft": {"value": "32px", "description": "Indented cell padding for hierarchy", "tailwind": "pl-8"}}}}, "statusBadges": {"completed": {"background": {"value": "#dcfce7", "description": "Completed status background", "tailwind": "bg-green-100"}, "color": {"value": "#166534", "description": "Completed status text color", "tailwind": "text-green-800"}, "code": {"value": "D25-DONE", "description": "Completed status code"}}, "inProgress": {"background": {"value": "#dbeafe", "description": "In progress status background", "tailwind": "bg-blue-100"}, "color": {"value": "#1e40af", "description": "In progress status text color", "tailwind": "text-blue-800"}, "code": {"value": "D25-ACTIVE", "description": "In progress status code"}}, "upcoming": {"background": {"value": "#f3f4f6", "description": "Upcoming status background", "tailwind": "bg-gray-100"}, "color": {"value": "#374151", "description": "Upcoming status text color", "tailwind": "text-gray-800"}, "code": {"value": "D25-WAVES", "description": "Upcoming status code"}}, "common": {"padding": {"horizontal": "8px", "vertical": "4px", "description": "Status badge padding", "tailwind": "px-2 py-1"}, "borderRadius": {"value": "9999px", "description": "Status badge border radius", "tailwind": "rounded-full"}, "fontSize": {"value": "14px", "description": "Status badge font size", "tailwind": "text-sm"}, "fontWeight": {"value": "500", "description": "Status badge font weight", "tailwind": "font-medium"}}}, "alloyBadge": {"background": {"value": "transparent", "description": "Alloy badge background", "tailwind": "bg-transparent"}, "border": {"value": "1px solid #d1d5db", "description": "Alloy badge border", "tailwind": "border border-gray-300"}, "color": {"value": "#374151", "description": "Alloy badge text color", "tailwind": "text-gray-700"}, "padding": {"horizontal": "8px", "vertical": "4px", "description": "Alloy badge padding", "tailwind": "px-2 py-1"}, "borderRadius": {"value": "6px", "description": "Alloy badge border radius", "tailwind": "rounded-md"}, "fontSize": {"value": "12px", "description": "Alloy badge font size", "tailwind": "text-xs"}, "fontWeight": {"value": "500", "description": "Alloy badge font weight", "tailwind": "font-medium"}}, "editableSelect": {"trigger": {"height": {"value": "32px", "description": "Select trigger height", "tailwind": "h-8"}, "fontSize": {"value": "14px", "description": "Select trigger font size", "tailwind": "text-sm"}, "border": {"value": "1px solid #d1d5db", "description": "Select trigger border", "tailwind": "border border-gray-300"}, "borderRadius": {"value": "6px", "description": "Select trigger border radius", "tailwind": "rounded-md"}, "focus": {"borderColor": {"value": "#00B2A1", "description": "Select trigger focus border color", "tailwind": "focus:border-[#00B2A1]"}, "ringColor": {"value": "rgba(0, 178, 161, 0.2)", "description": "Select trigger focus ring color", "tailwind": "focus:ring-[#00B2A1]/20"}}}}, "loadingSkeleton": {"background": {"value": "#f1f5f9", "description": "Loading skeleton background", "tailwind": "bg-slate-100"}, "animation": {"value": "pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite", "description": "Loading skeleton animation", "tailwind": "animate-pulse"}, "height": {"value": "20px", "description": "Loading skeleton height", "tailwind": "h-5"}, "borderRadius": {"value": "4px", "description": "Loading skeleton border radius", "tailwind": "rounded"}}, "emptyState": {"container": {"padding": {"value": "32px", "description": "Empty state container padding", "tailwind": "p-8"}, "textAlign": {"value": "center", "description": "Empty state text alignment", "tailwind": "text-center"}}, "icon": {"size": {"value": "48px", "description": "Empty state icon size", "tailwind": "w-12 h-12"}, "color": {"value": "#9ca3af", "description": "Empty state icon color", "tailwind": "text-gray-400"}, "marginBottom": {"value": "16px", "description": "Empty state icon bottom margin", "tailwind": "mb-4"}}, "title": {"fontSize": {"value": "18px", "description": "Empty state title font size", "tailwind": "text-lg"}, "fontWeight": {"value": "500", "description": "Empty state title font weight", "tailwind": "font-medium"}, "color": {"value": "#374151", "description": "Empty state title color", "tailwind": "text-gray-700"}}, "description": {"fontSize": {"value": "14px", "description": "Empty state description font size", "tailwind": "text-sm"}, "color": {"value": "#6b7280", "description": "Empty state description color", "tailwind": "text-gray-500"}}}}, "patterns": {"hierarchicalTable": {"description": "Design patterns for hierarchical table structures", "levelIndentation": {"level1": {"value": "0px", "description": "Top level indentation", "tailwind": "pl-0"}, "level2": {"value": "16px", "description": "Second level indentation", "tailwind": "pl-4"}, "level3": {"value": "32px", "description": "Third level indentation", "tailwind": "pl-8"}}, "expandCollapse": {"iconSize": {"parent": "20px", "child": "16px", "description": "Icon sizes for different hierarchy levels"}, "transition": {"value": "all 200ms ease-in-out", "description": "Expand/collapse transition", "tailwind": "transition-all duration-200"}}}, "tabNavigation": {"description": "Design patterns for tab navigation", "spacing": {"betweenTabs": {"value": "4px", "description": "Space between tabs", "tailwind": "gap-1"}, "containerPadding": {"value": "4px", "description": "Container internal padding", "tailwind": "p-1"}}, "states": {"transition": {"value": "all 150ms ease-in-out", "description": "State transition timing", "tailwind": "transition-all duration-150"}}}}, "accessibility": {"focusIndicators": {"color": {"value": "#00B2A1", "description": "Focus indicator color", "tailwind": "focus:ring-[#00B2A1]"}, "width": {"value": "2px", "description": "Focus indicator width", "tailwind": "focus:ring-2"}, "offset": {"value": "2px", "description": "Focus indicator offset", "tailwind": "focus:ring-offset-2"}}, "contrast": {"minimumRatio": {"value": "4.5:1", "description": "Minimum contrast ratio for text"}, "largeTextRatio": {"value": "3:1", "description": "Minimum contrast ratio for large text"}}, "interactiveElements": {"minimumSize": {"value": "44px", "description": "Minimum touch target size"}}}, "usageExamples": {"parentTabs": {"container": "bg-slate-100 h-12 rounded-md p-1", "tab": "px-6 py-2 text-md font-medium rounded-md transition-all duration-150", "activeTab": "bg-[#00B2A1] text-white font-bold shadow-md shadow-[#00B2A1]/20", "inactiveTab": "text-slate-500 hover:bg-[#00B2A1]/10 hover:text-[#00B2A1]"}, "mainTable": {"container": "bg-white rounded-xl border border-slate-200 shadow-sm max-h-[calc(100vh-230px)] overflow-y-auto", "header": "sticky top-0 bg-white z-10 border-b border-slate-200", "headerCell": "p-3 text-center font-semibold text-sm text-gray-700 whitespace-nowrap", "recipeHeader": "bg-blue-50 border-b border-blue-200 cursor-pointer hover:bg-blue-100 p-3", "scheduleRow": "bg-gray-100 border-b border-slate-200 cursor-pointer hover:bg-gray-200 p-3", "soItemRow": "bg-white border-b border-slate-100 hover:bg-gray-50 text-sm", "statusBadge": "px-2 py-1 rounded-full text-sm font-medium", "completedStatus": "bg-green-100 text-green-800", "inProgressStatus": "bg-blue-100 text-blue-800", "upcomingStatus": "bg-gray-100 text-gray-800"}}}}