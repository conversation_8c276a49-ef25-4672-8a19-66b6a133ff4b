"use client"

import type React from "react"

import { useState, useRef, type Drag<PERSON><PERSON> } from "react"
import { PageHeader } from "./components/ui/page-header/page-header"
import { useEffect } from "react"
import { DataNarrativeTable } from "./DataNarrativeTable"
import { TbDownload, TbShare3 } from "react-icons/tb"
// Define the narrative item type
type NarrativeItem = {
  id: string
  fileName: string
  fileType: string
  createdAt: string
  size: string
  status: "processed" | "processing" | "failed"
  [key: string]: any
}

// Define sort types
type SortColumn = keyof NarrativeItem | null
type SortDirection = "asc" | "desc"

// Define tone options
type SummaryTone = "formal" | "conversational" | "analytical"

export default function DataNarrativeApp() {
  const [isDetailView, setIsDetailView] = useState(false)
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const [summaryTone, setSummaryTone] = useState<SummaryTone>("formal")
  const [aiSummary, setAiSummary] = useState<string>("")
  const [sampleData, setSampleData] = useState<any[]>([])
  const [columnInfo, setColumnInfo] = useState<{
    count: number
    types: Record<string, string>
  }>({ count: 0, types: {} })
  const [rowCount, setRowCount] = useState<number>(0)
  const [detectedTrends, setDetectedTrends] = useState<string[]>([])

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setUploadedFile(event.target.files[0])
    }
  }

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragging(false)
  }

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragging(false)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      setUploadedFile(e.dataTransfer.files[0])
    }
  }

  // State for narratives data and sorting
  const [narratives, setNarratives] = useState<NarrativeItem[]>([])
  const [sortColumn, setSortColumn] = useState<SortColumn>("createdAt")
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc")
  const [selectedNarrativeId, setSelectedNarrativeId] = useState<string | null>(null)

  // Load sample data
  useEffect(() => {
    // This would normally come from an API
    const sampleData: NarrativeItem[] = [
      {
        id: "1",
        fileName: "Q3 Sales Report",
        fileType: "CSV",
        createdAt: "2023-10-15 14:30",
        size: "2.4 MB",
        status: "processed",
      },
      {
        id: "2",
        fileName: "Customer Feedback Analysis",
        fileType: "JSON",
        createdAt: "2023-10-12 09:15",
        size: "1.8 MB",
        status: "processed",
      },
      {
        id: "3",
        fileName: "Marketing Campaign Data",
        fileType: "XLSX",
        createdAt: "2023-10-10 16:45",
        size: "3.2 MB",
        status: "processing",
      },
      {
        id: "4",
        fileName: "Product Inventory",
        fileType: "CSV",
        createdAt: "2023-10-05 11:20",
        size: "0.9 MB",
        status: "failed",
      },
    ]

    setNarratives(sampleData)
  }, [])

  const handleBrowseClick = () => {
    fileInputRef.current?.click()
  }

  const handleCreateNarrative = () => {
    if (!uploadedFile) return

    setIsProcessing(true)

    // Simulate upload progress
    let progress = 0
    const interval = setInterval(() => {
      progress += 10
      setUploadProgress(progress)

      if (progress >= 100) {
        clearInterval(interval)
        setIsDetailView(true)
        setIsProcessing(false)
        setUploadProgress(0)
        // Add the new narrative to the list
        const newNarrative: NarrativeItem = {
          id: `new-${Date.now()}`,
          fileName: uploadedFile.name,
          fileType: uploadedFile.name.split(".").pop()?.toUpperCase() || "UNKNOWN",
          createdAt: new Date().toLocaleString(),
          size: `${(uploadedFile.size / (1024 * 1024)).toFixed(1)} MB`,
          status: "processed",
        }

        setNarratives((prev) => [newNarrative, ...prev])
      }
    }, 300)
  }

  const handleBackToList = () => {
    setIsDetailView(false)
    setSelectedNarrativeId(null)
  }

  const handleDownload = () => {
    // Implement download logic here
    console.log("Downloading report...")
  }

  const handleShare = () => {
    // Implement share logic here
    console.log("Sharing report...")
  }

  // Define views for the PageHeader
  const currentViewInfo = isDetailView
    ? {
        label: "Narrative Details",
        description: "View the detailed analysis and insights from your data",
      }
    : {
        label: "Data Narratives",
        description: "Create and manage your data narratives",
      }

  // Handle sorting
  const handleSort = (column: SortColumn) => {
    if (column === sortColumn) {
      // Toggle direction if same column
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      // Set new column and default to ascending
      setSortColumn(column)
      setSortDirection("asc")
    }
  }

  // Sort the narratives based on current sort settings
  const sortedNarratives = [...narratives].sort((a, b) => {
    if (!sortColumn) return 0

    const aValue = a[sortColumn]
    const bValue = b[sortColumn]

    if (aValue < bValue) return sortDirection === "asc" ? -1 : 1
    if (aValue > bValue) return sortDirection === "asc" ? 1 : -1
    return 0
  })

  // Get the selected narrative for detail view
  const selectedNarrative = narratives.find((n) => n.id === selectedNarrativeId)

  const handleSampleFile = () => {
    // Create a sample CSV file in memory
    const sampleData = `id,name,sales,region,date
        1,Product A,1250,North,2023-01-15
        2,Product B,890,South,2023-01-16
        3,Product C,1400,East,2023-01-17
        4,Product D,950,West,2023-01-18
        5,Product E,2100,North,2023-01-19
        6,Product F,760,South,2023-01-20
        7,Product G,1850,East,2023-01-21
        8,Product H,1340,West,2023-01-22`
    // Create a file object from the sample data
    const sampleFile = new File([sampleData], "sample-data.csv", {
      type: "text/csv",
    })

    // Set the sample file as the uploaded file
    setUploadedFile(sampleFile)
  }

  // Add a function to clear the uploaded file
  const handleClearFile = () => {
    setUploadedFile(null)
  }

  const handleToneChange = (tone: SummaryTone) => {
    setSummaryTone(tone)
    // In a real app, you would regenerate the summary with the new tone
    generateSummary(tone)
  }

  const handleRegenerateSummary = () => {
    generateSummary(summaryTone)
  }

  const generateSummary = (tone: SummaryTone) => {
    // This would be an API call in a real application
    // For now, we'll simulate it with different summaries based on tone
    setAiSummary(
      `This is a ${tone} summary of the data narrative. The data shows interesting patterns in sales across different regions.`,
    )

    // In a real app, you would also update the detected trends here
    setDetectedTrends([
      "Sales are consistently higher in the North region",
      "Product C shows the strongest growth trend",
      "January 19th had the highest overall sales",
      "Products in the East region show declining performance",
    ])
  }

  // Add a function to parse the file and extract metadata
  const parseFileData = (file: File | null) => {
    if (!file) return

    // For CSV files, we could parse them to extract row/column info
    // This is a simplified example - in a real app, you'd use a proper CSV parser
    const reader = new FileReader()
    reader.onload = (e) => {
      const text = e.target?.result as string
      const lines = text.split("\n")
      const headers = lines[0].split(",").map((h) => h.trim())

      // Extract sample data (first 5 rows)
      const sampleRows = lines.slice(1, 6).map((line) => {
        const values = line.split(",").map((v) => v.trim())
        return headers.reduce(
          (obj, header, i) => {
            obj[header] = values[i]
            return obj
          },
          {} as Record<string, string>,
        )
      })

      setSampleData(sampleRows)
      setRowCount(lines.length - 1) // Subtract header row

      // Determine column types (simplified)
      const types: Record<string, string> = {}
      headers.forEach((header) => {
        // Check first data row to guess type
        const value = sampleRows[0][header]
        if (!isNaN(Number(value))) {
          types[header] = "number"
        } else if (value.match(/^\d{4}-\d{2}-\d{2}$/)) {
          types[header] = "date"
        } else {
          types[header] = "string"
        }
      })

      setColumnInfo({
        count: headers.length,
        types,
      })

      // Generate the AI summary
      generateSummary(summaryTone)
    }

    reader.readAsText(file)
  }

  // Update the useEffect to call parseFileData when a file is uploaded
  useEffect(() => {
    if (uploadedFile) {
      parseFileData(uploadedFile)
    }
  }, [uploadedFile])

  // Also update handleViewNarrative to simulate parsing for existing narratives
  const handleViewNarrative = (id: string) => {
    setSelectedNarrativeId(id)
    setIsDetailView(true)

    // Simulate parsing data for the selected narrative
    // In a real app, you would fetch this data from an API
    const narrative = narratives.find((n) => n.id === id)
    if (narrative) {
      // Create sample data for the selected narrative
      setSampleData([
        {
          id: "1",
          name: "Product A",
          sales: "1250",
          region: "North",
          date: "2023-01-15",
        },
        {
          id: "2",
          name: "Product B",
          sales: "890",
          region: "South",
          date: "2023-01-16",
        },
        {
          id: "3",
          name: "Product C",
          sales: "1400",
          region: "East",
          date: "2023-01-17",
        },
        {
          id: "4",
          name: "Product D",
          sales: "950",
          region: "West",
          date: "2023-01-18",
        },
      ])

      setRowCount(8)
      setColumnInfo({
        count: 5,
        types: {
          id: "number",
          name: "string",
          sales: "number",
          region: "string",
          date: "date",
        },
      })

      generateSummary(summaryTone)
    }
  }

  // Add these new state variables for chart data
  const [salesData, setSalesData] = useState<{ label: string; value: number }[]>([
    { label: "Jan", value: 1200 },
    { label: "Feb", value: 1900 },
    { label: "Mar", value: 1500 },
    { label: "Apr", value: 2200 },
    { label: "May", value: 2700 },
    { label: "Jun", value: 2300 },
  ])

  const [categoryData, setSategoryData] = useState<{ category: string; value: number }[]>([
    { category: "North", value: 4200 },
    { category: "South", value: 3800 },
    { category: "East", value: 5100 },
    { category: "West", value: 3500 },
  ])

  // Add a function to calculate the maximum value for scaling
  const getMaxValue = (data: any[], valueKey: string): number => {
    return Math.max(...data.map((item) => item[valueKey]))
  }

  return (
    <div className="container mx-auto">
      <PageHeader
        title=""
        parentPath="/deployed-projects"
        parentName="Deployed Projects"
        subViews={[{ label: currentViewInfo.label }]}
        currentSubView={currentViewInfo.label}
        defaultDescription={currentViewInfo.description}
        disableDropdown={true}
      />
      <div className="p-6">
        {!isDetailView ? (
          <div className="space-y-8">
            {/* Upload Section */}
            <div className="w-full">
              <div
                className={`flex flex-col items-start justify-center px-8 py-6 border-4 border-dashed rounded-xl h-fit transition-colors ${
                  isDragging ? "border-teal-500 bg-teal-50" : "border-gray-300"
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <h2 className="text-2xl text-slate-400 font-medium text-left mb-2">
                  Add a file to create a new Narrative{" "}
                </h2>
                <div className="flex flex-row items-center justify-between w-full h-[72px]">
                  <div className="flex flex-row items-center">
                    <input
                      type="file"
                      onChange={handleFileUpload}
                      className="hidden"
                      id="fileUpload"
                      ref={fileInputRef}
                      accept=".csv,.json,.xls,.xlsx"
                    />

                    {/* Upload Icon */}
                    <svg
                      className="w-12 h-12 text-slate-400 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                      />
                    </svg>

                    <div className="flex flex-col items-start">
                      <p className="text-xl text-left font-bold text-slate-700">
                        {uploadedFile ? uploadedFile.name : "Drag & drop your file here"}
                      </p>
                      <p className="text-md text-slate-400">Supported formats: CSV, JSON, XLS, XLSX</p>
                    </div>

                    {!uploadedFile ? (
                      <>
                        <div className="flex flex-col items-center text-base text-slate-400 mx-8">
                          <span>|</span>
                          <p className=" font-medium">or</p>
                          <span>|</span>
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={handleBrowseClick}
                            className="bg-teal-500 text-white px-4 py-2 rounded-md hover:bg-teal-600 transition"
                          >
                            Browse Files
                          </button>
                          <button
                            onClick={handleSampleFile}
                            className="bg-white text-blue-500 px-4 py-2 rounded-md hover:underline transition"
                          >
                            Try Sample File
                          </button>
                        </div>
                      </>
                    ) : (
                      <button
                        onClick={handleClearFile}
                        className="ml-8 border-2 border-red-500 rounded-full text-red-500 hover:text-red-700 transition"
                      >
                        <svg
                          className="w-6 h-6"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    )}
                  </div>

                  {/* Right Column - Create New Narrative button */}
                  <div className="flex flex-col justify-center items-center">
                    {uploadedFile && !isProcessing && (
                      <button
                        onClick={handleCreateNarrative}
                        className="w-full max-w-xs py-3 px-6 rounded-md text-white font-medium transition bg-teal-500 hover:bg-teal-600"
                      >
                        + Create New Narrative
                      </button>
                    )}

                    {isProcessing && (
                      <div className="w-full max-w-xs mt-4">
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div
                            className="bg-teal-500 h-2.5 rounded-full transition-all duration-300"
                            style={{ width: `${uploadProgress}%` }}
                          ></div>
                        </div>
                        <p className="text-sm text-gray-500 mt-2 text-center">Processing... {uploadProgress}%</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            {/* List of Narratives */}
            <div className="space-y-4">
              <div>
                <h2 className="text-xl font-bold mb-1">Your Narratives</h2>
                <p className="text-gray-500 mb-4">Your previously created narratives will appear here.</p>
              </div>

              {/* Narrative Table */}
              <DataNarrativeTable
                data={sortedNarratives}
                onSort={handleSort}
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onViewNarrative={handleViewNarrative}
              />
            </div>{" "}
          </div>
        ) : (
          <div className="space-y-8">
            <div className="flex justify-between items-center mb-0">
              <div className="flex flex-col items-start">
                {/* Back button */}
                <button
                  onClick={handleBackToList}
                  className="flex text-left mb-2 text-slate-600 hover:text-slate-800 transition"
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                  </svg>
                  Back to Narratives
                </button>
                <h1 className="text-3xl font-bold">{selectedNarrative?.fileName}</h1>
              </div>
              {/* Download and Share buttons */}
              <div className="flex justify-end gap-4 mt-4">
                <button
                  onClick={handleDownload}
                  className="flex flex-row items-center border-teal-500 border-2 text-teal-500 px-6 py-2 rounded-md hover:bg-teal-600 hover:text-white"
                >
                  <TbDownload className="mr-2 h-5 w-5" />
                  Download Report
                </button>
                <button
                  onClick={handleShare}
                  className="flex flex-row items-center border-teal-500 border-2 text-teal-500 px-6 py-2 rounded-md hover:bg-teal-600 hover:text-white"
                >
                  <TbShare3 className="mr-2 h-5 w-5" />
                  Share Report
                </button>
              </div>
            </div>

            {/* AI Generated Summary - Full Width */}
            <section className="p-6 border rounded-lg">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold">Summary</h2>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">Tone:</span>
                    <select
                      value={summaryTone}
                      onChange={(e) => handleToneChange(e.target.value as SummaryTone)}
                      className="border rounded-md px-2 py-1 text-sm"
                    >
                      <option value="formal">Formal</option>
                      <option value="conversational">Conversational</option>
                      <option value="analytical">Analytical</option>
                    </select>
                  </div>
                  <button
                    onClick={handleRegenerateSummary}
                    className="flex items-center text-teal-600 hover:text-teal-800"
                  >
                    <svg
                      className="w-4 h-4 mr-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                      />
                    </svg>
                    Regenerate
                  </button>
                </div>
              </div>
              <div className="bg-gray-50 p-4 rounded-md">
                <p className="text-gray-800">{aiSummary}</p>
              </div>
            </section>

            {/* Data Summary and Preview - Split Row */}
            <div className="flex flex-col md:flex-row gap-6">
              {/* Data Preview - 2/3 Width */}
              <section className="p-6 border rounded-lg md:w-2/3">
                <h2 className="text-xl font-bold mb-4">Data Preview</h2>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        {sampleData.length > 0 &&
                          Object.keys(sampleData[0]).map((header) => (
                            <th
                              key={header}
                              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                            >
                              {header}
                            </th>
                          ))}
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {sampleData.map((row, rowIndex) => (
                        <tr key={rowIndex}>
                          {Object.values(row).map((value, cellIndex) => (
                            <td key={cellIndex} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {String(value)}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </section>

              {/* Data Summary - 1/3 Width */}
              <section className="p-6 border rounded-lg md:w-1/3">
                <h2 className="text-xl font-bold mb-4">Data Summary</h2>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">File Name:</span>
                    <span className="font-medium">{selectedNarrative?.fileName || uploadedFile?.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">File Type:</span>
                    <span className="font-medium">{selectedNarrative?.fileType || uploadedFile?.type}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Created:</span>
                    <span className="font-medium">{selectedNarrative?.createdAt || new Date().toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Size:</span>
                    <span className="font-medium">
                      {selectedNarrative?.size ||
                        (uploadedFile ? `${(uploadedFile.size / (1024 * 1024)).toFixed(1)} MB` : "")}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Rows:</span>
                    <span className="font-medium">{rowCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Columns:</span>
                    <span className="font-medium">{columnInfo.count}</span>
                  </div>
                  <div className="mt-4">
                    <h3 className="text-md font-semibold mb-2">Column Types:</h3>
                    <div className="space-y-1">
                      {Object.entries(columnInfo.types).map(([column, type]) => (
                        <div key={column} className="flex justify-between text-sm">
                          <span className="text-gray-600">{column}:</span>
                          <span className="font-medium">{type}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </section>
            </div>
            {/* Charts - Two Half-Width Sections */}
            <div className="flex flex-col md:flex-row gap-6">
              {/* Chart 1 - Numeric Data Visualization (Sales Over Time) */}
              <section className="p-6 border rounded-lg md:w-1/2">
                <h2 className="text-xl font-bold mb-4">Sales Trend Over Time</h2>
                <div className="h-64 bg-white p-4">
                  {/* Simple line chart visualization */}
                  <div className="relative h-full flex flex-col">
                    {/* Y-axis labels */}
                    <div className="absolute left-0 top-0 bottom-0 w-12 flex flex-col justify-between text-xs text-gray-500">
                      <span>$3000</span>
                      <span>$2250</span>
                      <span>$1500</span>
                      <span>$750</span>
                      <span>$0</span>
                    </div>

                    {/* Chart area */}
                    <div className="ml-12 h-full flex flex-col">
                      {/* Chart grid lines */}
                      <div className="flex-1 border-b border-gray-200 relative">
                        <div className="absolute left-0 right-0 top-0 border-t border-gray-200"></div>
                        <div className="absolute left-0 right-0 top-1/4 border-t border-gray-200"></div>
                        <div className="absolute left-0 right-0 top-2/4 border-t border-gray-200"></div>
                        <div className="absolute left-0 right-0 top-3/4 border-t border-gray-200"></div>

                        {/* Line chart */}
                        <svg className="absolute inset-0 h-full w-full" preserveAspectRatio="none">
                          <polyline
                            points={salesData
                              .map((item, index) => {
                                const x = (index / (salesData.length - 1)) * 100
                                const y = 100 - (item.value / 3000) * 100
                                return `${x},${y}`
                              })
                              .join(" ")}
                            fill="none"
                            stroke="#14b8a6"
                            strokeWidth="2"
                            vectorEffect="non-scaling-stroke"
                          />

                          {/* Data points */}
                          {salesData.map((item, index) => {
                            const x = (index / (salesData.length - 1)) * 100
                            const y = 100 - (item.value / 3000) * 100
                            return (
                              <circle
                                key={index}
                                cx={`${x}%`}
                                cy={`${y}%`}
                                r="4"
                                fill="white"
                                stroke="#14b8a6"
                                strokeWidth="2"
                              />
                            )
                          })}
                        </svg>
                      </div>

                      {/* X-axis labels */}
                      <div className="h-6 flex justify-between text-xs text-gray-500 pt-1">
                        {salesData.map((item, index) => (
                          <span key={index} className="text-center" style={{ width: `${100 / salesData.length}%` }}>
                            {item.label}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="mt-2 text-sm text-gray-500 text-center">
                  Monthly sales performance showing an upward trend
                </div>
              </section>

              {/* Chart 2 - Categorical Data Visualization (Sales by Region) */}
              <section className="p-6 border rounded-lg md:w-1/2">
                <h2 className="text-xl font-bold mb-4">Sales by Region</h2>
                <div className="h-64 bg-white p-4">
                  {/* Simple bar chart visualization */}
                  <div className="relative h-full flex flex-col">
                    {/* Y-axis labels */}
                    <div className="absolute left-0 top-0 bottom-0 w-12 flex flex-col justify-between text-xs text-gray-500">
                      <span>$6000</span>
                      <span>$4500</span>
                      <span>$3000</span>
                      <span>$1500</span>
                      <span>$0</span>
                    </div>

                    {/* Chart area */}
                    <div className="ml-12 h-full flex flex-col">
                      {/* Chart grid lines */}
                      <div className="flex-1 border-b border-gray-200 relative">
                        <div className="absolute left-0 right-0 top-0 border-t border-gray-200"></div>
                        <div className="absolute left-0 right-0 top-1/4 border-t border-gray-200"></div>
                        <div className="absolute left-0 right-0 top-2/4 border-t border-gray-200"></div>
                        <div className="absolute left-0 right-0 top-3/4 border-t border-gray-200"></div>

                        {/* Bar chart */}
                        <div className="absolute inset-0 flex items-end justify-around">
                          {categoryData.map((item, index) => {
                            const height = (item.value / 6000) * 100
                            let barColor

                            // Different colors for different regions
                            switch (item.category) {
                              case "North":
                                barColor = "#14b8a6"
                                break // teal
                              case "South":
                                barColor = "#0ea5e9"
                                break // sky blue
                              case "East":
                                barColor = "#8b5cf6"
                                break // purple
                              case "West":
                                barColor = "#f59e0b"
                                break // amber
                              default:
                                barColor = "#14b8a6"
                            }

                            return (
                              <div key={index} className="flex flex-col items-center w-1/6">
                                <div
                                  className="w-16 rounded-t-md transition-all duration-500 ease-in-out"
                                  style={{
                                    height: `${height}%`,
                                    backgroundColor: barColor,
                                  }}
                                ></div>
                              </div>
                            )
                          })}
                        </div>
                      </div>

                      {/* X-axis labels */}
                      <div className="h-6 flex justify-around text-xs text-gray-500 pt-1">
                        {categoryData.map((item, index) => (
                          <span key={index} className="text-center">
                            {item.category}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="mt-2 text-sm text-gray-500 text-center">
                  East region shows the highest sales performance
                </div>
              </section>
            </div>

            {/* Detected Trends Section */}
            <section className="p-6 border rounded-lg">
              <h2 className="text-xl font-bold mb-4">Detected Trends</h2>
              <div className="space-y-4">
                {detectedTrends.length > 0 ? (
                  <ul className="space-y-2">
                    {detectedTrends.map((trend, index) => (
                      <li key={index} className="flex items-start">
                        <svg
                          className="w-5 h-5 text-teal-500 mr-2 mt-0.5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                        </svg>
                        <span className="text-gray-700">{trend}</span>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-gray-500">No trends detected in this dataset.</p>
                )}
              </div>
            </section>
          </div>
        )}
      </div>
    </div>
  )
}

