"use client";

import type React from "react";

import { useState, useRef, type DragEvent, useEffect } from "react";
import { PageHeader } from "./ui/page-header";
import { DataNarrativeTable } from "./DataNarrativeTable";
import { TbDownload, TbShare3 } from "react-icons/tb";
import { LoadingSpinner } from "./LoadingSpinner";
import { NarrativeDisplay } from "./NarrativeDisplay";
import { ShareModal } from "./ShareModal";
import {
  uploadFile,
  processFile,
  generateNarrative,
  fetchNarratives,
  fetchNarrativeById,
  type SummaryTone,
  type NarrativeItem as ApiNarrativeItem,
} from "../services/api";
import { generatePDF } from "../utils/pdf-generator";
import { toast } from "react-hot-toast";
import { PieChart } from "./PieChart";

// Define the narrative item type
type NarrativeItem = {
  id: string;
  fileName: string;
  fileType: string;
  createdAt: string;
  size: string;
  status: "processed" | "processing" | "failed";
  tone?: string;
  filePath?: string;
  [key: string]: any;
};

// Define sort types
type SortColumn = keyof NarrativeItem | null;
type SortDirection = "asc" | "desc";

export default function DataNarrativeApp() {
  const [isDetailView, setIsDetailView] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [isGeneratingNarrative, setIsGeneratingNarrative] = useState(false);
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [isLoadingNarratives, setIsLoadingNarratives] = useState(false);
  const [isLoadingNarrativeDetail, setIsLoadingNarrativeDetail] =
    useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const chartRefs = useRef<Array<HTMLElement | null>>([]);

  const [summaryTone, setSummaryTone] = useState<SummaryTone>("formal");
  const [aiSummary, setAiSummary] = useState<string>("");
  const [sampleData, setSampleData] = useState<any[]>([]);
  const [columnInfo, setColumnInfo] = useState<{
    count: number;
    types: Record<string, string>;
  }>({ count: 0, types: {} });
  const [rowCount, setRowCount] = useState<number>(0);
  const [detectedTrends, setDetectedTrends] = useState<string[]>([]);
  const [uploadedFilePath, setUploadedFilePath] = useState<string>("");
  const [processedInsights, setProcessedInsights] = useState<any>(null);

  // State for narratives data and sorting
  const [narratives, setNarratives] = useState<NarrativeItem[]>([]);
  const [sortColumn, setSortColumn] = useState<SortColumn>("createdAt");
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc");
  const [selectedNarrativeId, setSelectedNarrativeId] = useState<string | null>(
    null
  );

  // Chart data state
  const [salesData, setSalesData] = useState<
    Array<{ label: string; value: number }>
  >([]);
  const [categoryData, setCategoryData] = useState<
    Array<{ category: string; value: number }>
  >([]);

  // State for column selection
  const [numericColumns, setNumericColumns] = useState<string[]>([]);
  const [categoricalColumns, setCategoricalColumns] = useState<string[]>([]);
  const [selectedNumericColumn, setSelectedNumericColumn] = useState<
    string | null
  >(null);
  const [selectedCategoricalColumn, setSelectedCategoricalColumn] = useState<
    string | null
  >(null);

  // Add this after the other state declarations
  const [isChartLoading, setIsChartLoading] = useState<boolean>(false);

  // Fetch narratives from API on component mount
  useEffect(() => {
    loadNarratives();
  }, []);

  // Function to load narratives from API
  const loadNarratives = async () => {
    setIsLoadingNarratives(true);
    try {
      const response = await fetchNarratives();
      if (response.success) {
        // Convert API narrative items to our format
        const formattedNarratives: NarrativeItem[] = response.data.map(
          (item: ApiNarrativeItem) => ({
            id: item.id.toString(),
            fileName: item.file_name,
            fileType: item.file_type.toUpperCase(),
            createdAt: formatDate(item.created_at),
            size: formatFileSize(item.file_size),
            status: item.status as "processed" | "processing" | "failed", // Explicitly cast status
            tone: item.tone,
            filePath: item.file_path,
          })
        );
        setNarratives(formattedNarratives);
      } else {
        toast.error(response.error || "Failed to load narratives");
      }
    } catch (error) {
      console.error("Error loading narratives:", error);
      toast.error("Failed to load narratives");
    } finally {
      setIsLoadingNarratives(false);
    }
  };

  // Format date for display
  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString();
    } catch (error) {
      return dateString;
    }
  };

  const handleNumericColumnChange = (column: string) => {
    setIsChartLoading(true);
    setSelectedNumericColumn(column);

    try {
      // Update chart data based on selected column
      console.log("processedInsights", processedInsights);
      if (processedInsights && processedInsights.numeric_stats) {
        const numericData = processedInsights.numeric_stats[column];

        if (numericData && numericData.unique_values) {
          // Take a subset of values for the chart (up to 6)
          const values = numericData.unique_values.slice(
            0,
            Math.min(6, numericData.unique_values.length)
          );

          // Create x-axis labels based on row indices
          const labels: string[] = values.map(
            (_: number, index: number): string => `Row ${index + 1}`
          );

          // Create chart data
          const chartData = values.map((value: number, index: number) => ({
            label: labels[index],
            value: value,
          }));
          console.log("chartData", chartData);
          setSalesData(chartData);
        } else {
          // Handle case when no data is available for the selected column
          setSalesData([]);
          // toast.warning(`No numeric data available for ${column}`);
        }
      } else {
        // Handle case when no chart data is available
        setSalesData([]);
        // toast.warning("No chart data available");
      }
    } finally {
      // Ensure loading state is always turned off, even if there's an error
      setIsChartLoading(false);
    }
  };

  const handleCategoricalColumnChange = (column: string) => {
    setIsChartLoading(true);
    setSelectedCategoricalColumn(column);

    try {
      // Update chart data based on selected column
      if (
        processedInsights &&
        processedInsights.categorical_stats &&
        processedInsights.categorical_stats[column]
      ) {
        const categoricalData = processedInsights.categorical_stats[column];

        if (categoricalData) {
          // Convert object to array of category-value pairs
          const chartData = Object.entries(categoricalData)
            .slice(0, 6) // Take up to 6 categories
            .map(([category, value]: [string, any]) => ({
              category,
              value: typeof value === "number" ? value : 1,
            }));

          setCategoryData(chartData);
        } else {
          // Handle case when no data is available for the selected column
          setCategoryData([]);
          // toast.warning(`No categorical data available for ${column}`);
        }
      } else {
        // Handle case when no chart data is available
        setCategoryData([]);
        // toast.warning("No chart data available");
      }
    } finally {
      // Ensure loading state is always turned off, even if there's an error
      setIsChartLoading(false);
    }
  };

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setUploadedFile(file);
      await uploadFileToServer(file);
    }
  };

  const uploadFileToServer = async (file: File) => {
    setIsUploading(true);
    setUploadError(null);

    try {
      const response = await uploadFile(file);

      if (response.success && response.file_info) {
        setUploadedFilePath(response.file_info.file_path);
        toast.success("File uploaded successfully!");
      } else {
        setUploadError(response.error || "Failed to upload file");
        toast.error(response.error || "Failed to upload file");
      }
    } catch (error) {
      console.error("Error uploading file:", error);
      setUploadError("An unexpected error occurred");
      toast.error("An unexpected error occurred during upload");
    } finally {
      setIsUploading(false);
    }
  };

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = async (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      setUploadedFile(file);
      await uploadFileToServer(file);
    }
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  // Update the handleCreateNarrative function to immediately navigate to detail view
  const handleCreateNarrative = async () => {
    if (!uploadedFile || !uploadedFilePath) return;

    setIsProcessing(true);
    setUploadProgress(0);

    // Create narrative object immediately
    const newNarrative: NarrativeItem = {
      id: `new-${Date.now()}`,
      fileName: uploadedFile.name,
      fileType: uploadedFile.name.split(".").pop()?.toUpperCase() || "UNKNOWN",
      createdAt: new Date().toLocaleString(),
      size: formatFileSize(uploadedFile.size),
      status: "processing",
      filePath: uploadedFilePath,
      tone: summaryTone,
    };

    // Add to narratives list and navigate to detail view
    setNarratives((prev) => [newNarrative, ...prev]);
    setSelectedNarrativeId(newNarrative.id);
    setIsDetailView(true);
    setIsGeneratingNarrative(true);

    // Start progress animation
    const progressInterval = setInterval(() => {
      setUploadProgress((prev) => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return 90;
        }
        return prev + 10;
      });
    }, 300);

    try {
      // Call the process API
      const processResponse = await processFile(uploadedFilePath);

      if (processResponse.success) {
        // Update state with the processed data
        setProcessedInsights(processResponse.insights);
        setDetectedTrends(processResponse.trends || []);

        // Update chart data if available
        if (processResponse.chart_data) {
          updateChartData(processResponse.chart_data);
        }

        // Extract sample data and column info
        extractDataFromResponse(processResponse);

        // Update narrative status
        setNarratives((prev) =>
          prev.map((n) =>
            n.id === newNarrative.id ? { ...n, status: "processed" } : n
          )
        );

        // Generate the narrative
        await generateNarrativeText(
          processResponse.insights,
          processResponse.trends || []
        );

        toast.success("Narrative created successfully!");

        // Refresh the narratives list to get the newly created narrative
        await loadNarratives();
      } else {
        toast.error(processResponse.error || "Failed to process file");
        // Update narrative status to failed
        setNarratives((prev) =>
          prev.map((n) =>
            n.id === newNarrative.id ? { ...n, status: "failed" } : n
          )
        );
      }
    } catch (error) {
      console.error("Error processing file:", error);
      toast.error("An error occurred while processing the file");
      // Update narrative status to failed
      if (selectedNarrativeId) {
        setNarratives((prev) =>
          prev.map((n) =>
            n.id === selectedNarrativeId ? { ...n, status: "failed" } : n
          )
        );
      }
    } finally {
      clearInterval(progressInterval);
      setUploadProgress(100);
      setTimeout(() => {
        setIsProcessing(false);
        setUploadProgress(0);
      }, 500);
    }
  };

  // Update the extractDataFromResponse function to use preview data from upload response
  const extractDataFromResponse = (response: any) => {
    // Extract row count
    setRowCount(response.insights.rows || 0);

    // Extract column info
    setColumnInfo({
      count: response.insights.columns || 0,
      types: response.insights.column_types || {},
    });

    // Extract sample data from file_info.preview if available
    if (
      response.file_info &&
      response.file_info.preview &&
      response.file_info.preview.length > 0
    ) {
      setSampleData(response.file_info.preview);
    } else {
      // Create sample data based on column types
      const sampleRows: any[] = [];
      if (response.insights.column_types) {
        const columnNames = Object.keys(response.insights.column_types);

        // Create 5 sample rows
        for (let i = 0; i < 5; i++) {
          const row: Record<string, any> = {};
          columnNames.forEach((col) => {
            // Add sample data based on column type
            const type = response.insights.column_types[col];
            if (type === "numeric") {
              row[col] = Math.floor(Math.random() * 1000);
            } else if (type === "date" || type === "datetime") {
              row[col] = new Date().toISOString().split("T")[0];
            } else {
              row[col] = `Sample ${col} ${i + 1}`;
            }
          });
          sampleRows.push(row);
        }
        setSampleData(sampleRows);
      }
    }

    // Extract numeric and categorical columns for dropdowns
    if (response.insights.column_types) {
      const numericColumns: string[] = [];
      const categoricalColumns: string[] = [];

      Object.entries(response.insights.column_types).forEach(
        ([column, type]) => {
          if (type === "numeric") {
            numericColumns.push(column);
          } else if (type === "categorical") {
            categoricalColumns.push(column);
          }
        }
      );

      setNumericColumns(numericColumns);
      setCategoricalColumns(categoricalColumns);

      // Set default selected columns if available
      if (numericColumns.length > 0) {
        setSelectedNumericColumn(numericColumns[0]);
        handleNumericColumnChange(numericColumns[0]);
      }
      if (categoricalColumns.length > 0) {
        setSelectedCategoricalColumn(categoricalColumns[0]);
        handleCategoricalColumnChange(categoricalColumns[0]);
      }
    }
  };

  const updateChartData = (chartData: any) => {
    // Update sales data if numeric data is available
    if (chartData.numeric && Object.keys(chartData.numeric).length > 0) {
      const numericKey = Object.keys(chartData.numeric)[0];
      setSelectedNumericColumn(numericKey);

      const numericData = chartData.numeric[numericKey];

      if (numericData && numericData.values && numericData.values.length > 0) {
        // Take a subset of values for the chart (up to 6)
        const values = numericData.values.slice(
          0,
          Math.min(6, numericData.values.length)
        );

        // Create x-axis labels based on row indices
        const labels: string[] = values.map(
          (_: number, index: number): string => `Row ${index + 1}`
        );

        // Create chart data
        const salesChartData = values.map((value: number, index: number) => ({
          label: labels[index],
          value: value,
        }));

        setSalesData(salesChartData);
      } else {
        setSalesData([]);
      }
    } else {
      setSalesData([]);
    }

    // Update category data if categorical data is available
    if (
      chartData.categorical &&
      Object.keys(chartData.categorical).length > 0
    ) {
      const categoricalKey = Object.keys(chartData.categorical)[0];
      setSelectedCategoricalColumn(categoricalKey);

      const categoricalData = chartData.categorical[categoricalKey];

      if (categoricalData && Object.keys(categoricalData).length > 0) {
        const categoryChartData = Object.entries(categoricalData)
          .slice(0, 6) // Take up to 6 categories
          .map(([category, value]: [string, any]) => ({
            category,
            value: typeof value === "number" ? value : 1,
          }));

        setCategoryData(categoryChartData);
      } else {
        setCategoryData([]);
      }
    } else {
      setCategoryData([]);
    }
  };

  const generateNarrativeText = async (insights: any, trends: string[]) => {
    setIsGeneratingNarrative(true);

    try {
      const response = await generateNarrative(
        insights,
        trends,
        summaryTone,
        uploadedFilePath
      );

      if (response.success) {
        setAiSummary(response.narrative);
      } else {
        toast.error(response.error || "Failed to generate narrative");
        setAiSummary("Failed to generate narrative. Please try again.");
      }
    } catch (error) {
      console.error("Error generating narrative:", error);
      setAiSummary("An error occurred while generating the narrative.");
    } finally {
      setIsGeneratingNarrative(false);
    }
  };

  const handleBackToList = () => {
    setIsDetailView(false);
    setSelectedNarrativeId(null);
  };

  // Update the handleDownload function to include trends and tone
  const handleDownload = async () => {
    // Get the chart elements
    const chartElements = chartRefs.current.filter(
      (ref) => ref !== null
    ) as HTMLElement[];

    try {
      // Generate PDF
      const fileName =
        selectedNarrative?.fileName || uploadedFile?.name || "Data Narrative";
      const pdfUrl = await generatePDF(
        fileName,
        aiSummary,
        chartElements,
        detectedTrends,
        summaryTone
      );

      // Create a link element and trigger download
      const link = document.createElement("a");
      link.href = pdfUrl;
      link.download = `${fileName.replace(/\s+/g, "_")}_Report.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success("Report downloaded successfully!");
    } catch (error) {
      console.error("Error downloading report:", error);
      toast.error("Failed to download report");
    }
  };

  const handleShare = () => {
    setIsShareModalOpen(true);
  };

  // Define views for the PageHeader
  const currentViewInfo = isDetailView
    ? {
        label: "Narrative Details",
        description: "View the detailed analysis and insights from your data",
      }
    : {
        label: "Data Narratives",
        description: "Create and manage your data narratives",
      };

  // Handle sorting
  const handleSort = (column: SortColumn) => {
    if (column === sortColumn) {
      // Toggle direction if same column
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // Set new column and default to ascending
      setSortColumn(column);
      setSortDirection("asc");
    }
  };

  // Sort the narratives based on current sort settings
  const sortedNarratives = [...narratives].sort((a, b) => {
    if (!sortColumn) return 0;

    const aValue = a[sortColumn];
    const bValue = b[sortColumn];

    if (aValue < bValue) return sortDirection === "asc" ? -1 : 1;
    if (aValue > bValue) return sortDirection === "asc" ? 1 : -1;
    return 0;
  });

  // Get the selected narrative for detail view
  const selectedNarrative = narratives.find(
    (n) => n.id === selectedNarrativeId
  );

  const handleSampleFile = async () => {
    // Create a sample CSV file in memory
    const sampleData = `id,name,sales,region,date
1,Product A,1250,North,2023-01-15
2,Product B,890,South,2023-01-16
3,Product C,1400,East,2023-01-17
4,Product D,950,West,2023-01-18
5,Product E,2100,North,2023-01-19
6,Product F,760,South,2023-01-20
7,Product G,1850,East,2023-01-21
8,Product H,1340,West,2023-01-22`;

    // Create a file object from the sample data
    const sampleFile = new File([sampleData], "sample-data.csv", {
      type: "text/csv",
    });

    // Set the sample file as the uploaded file
    setUploadedFile(sampleFile);

    // Upload the sample file
    await uploadFileToServer(sampleFile);
  };

  // Add a function to clear the uploaded file
  const handleClearFile = () => {
    setUploadedFile(null);
    setUploadedFilePath("");
    setUploadError(null);
  };

  // Update the handleViewNarrative function to properly process the file path
  const handleViewNarrative = async (id: string) => {
    setSelectedNarrativeId(id);
    setIsDetailView(true);
    setIsLoadingNarrativeDetail(true);

    try {
      // Fetch narrative details from API
      const response = await fetchNarrativeById(id);

      if (response.success && response.data) {
        const narrative = response.data;

        // Set narrative data
        setAiSummary(narrative.generated_text || "");
        setSummaryTone((narrative.tone as SummaryTone) || "formal");
        setUploadedFilePath(narrative.file_path);

        // Process the file to get insights
        if (narrative.file_path) {
          await processNarrativeFile(narrative.file_path);
        } else {
          toast.error("File path not found in narrative data");
          createSampleData();
        }
      } else {
        toast.error(response.error || "Failed to load narrative details");
        createSampleData();
      }
    } catch (error) {
      console.error("Error loading narrative details:", error);
      toast.error("An error occurred while loading narrative details");
      createSampleData();
    } finally {
      setIsLoadingNarrativeDetail(false);
    }
  };

  // Add a new function to process the narrative file
  const processNarrativeFile = async (filePath: string) => {
    try {
      const processResponse = await processFile(filePath);

      if (processResponse.success) {
        // Update state with the processed data
        setProcessedInsights(processResponse.insights);
        setDetectedTrends(processResponse.trends || []);

        // Update chart data if available
        if (processResponse.chart_data) {
          updateChartData(processResponse.chart_data);
        }

        // Extract sample data and column info
        extractDataFromResponse(processResponse);

        return true;
      } else {
        toast.error("Failed to process file data");
        return false;
      }
    } catch (error) {
      console.error("Error processing file:", error);
      toast.error("An error occurred while processing the file");
      return false;
    }
  };

  // Update the handleToneChange function to use the file path
  const handleToneChange = async (tone: SummaryTone) => {
    setSummaryTone(tone);

    // Regenerate the narrative with the new tone
    if (processedInsights && uploadedFilePath) {
      setIsGeneratingNarrative(true);
      try {
        const response = await generateNarrative(
          processedInsights,
          detectedTrends,
          tone,
          uploadedFilePath
        );

        if (response.success) {
          setAiSummary(response.narrative);
        } else {
          toast.error(response.error || "Failed to generate narrative");
        }
      } catch (error) {
        console.error("Error generating narrative:", error);
        toast.error("An error occurred while generating the narrative");
      } finally {
        setIsGeneratingNarrative(false);
      }
    }
  };

  // Update the handleRegenerateSummary function to use the file path
  const handleRegenerateSummary = async () => {
    if (processedInsights && uploadedFilePath) {
      setIsGeneratingNarrative(true);
      try {
        const response = await generateNarrative(
          processedInsights,
          detectedTrends,
          summaryTone,
          uploadedFilePath
        );

        if (response.success) {
          setAiSummary(response.narrative);
        } else {
          toast.error(response.error || "Failed to generate narrative");
        }
      } catch (error) {
        console.error("Error generating narrative:", error);
        toast.error("An error occurred while generating the narrative");
      } finally {
        setIsGeneratingNarrative(false);
      }
    }
  };

  // Helper function to create sample data when API fails
  const createSampleData = () => {
    setSampleData([
      {
        id: "1",
        name: "Product A",
        sales: "1250",
        region: "North",
        date: "2023-01-15",
      },
      {
        id: "2",
        name: "Product B",
        sales: "890",
        region: "South",
        date: "2023-01-16",
      },
      {
        id: "3",
        name: "Product C",
        sales: "1400",
        region: "East",
        date: "2023-01-17",
      },
      {
        id: "4",
        name: "Product D",
        sales: "950",
        region: "West",
        date: "2023-01-18",
      },
    ]);

    setRowCount(8);
    setColumnInfo({
      count: 5,
      types: {
        id: "number",
        name: "string",
        sales: "number",
        region: "string",
        date: "date",
      },
    });

    // Set numeric columns
    setNumericColumns(["id", "sales"]);
    setSelectedNumericColumn("sales");

    // Set categorical columns
    setCategoricalColumns(["region", "name"]);
    setSelectedCategoricalColumn("region");

    // Set sample chart data with row indices
    setSalesData([
      { label: "Row 1", value: 1250 },
      { label: "Row 2", value: 890 },
      { label: "Row 3", value: 1400 },
      { label: "Row 4", value: 950 },
      { label: "Row 5", value: 2100 },
      { label: "Row 6", value: 760 },
    ]);

    setCategoryData([
      { category: "North", value: 4200 },
      { category: "South", value: 3800 },
      { category: "East", value: 5100 },
      { category: "West", value: 3500 },
    ]);

    // Set sample trends
    setDetectedTrends([
      "Sales are consistently higher in the North region",
      "Product C shows the strongest growth trend",
      "January 19th had the highest overall sales",
      "Products in the East region show the highest average value",
    ]);
  };

  // Generate a share URL for the narrative
  const getShareUrl = () => {
    const baseUrl = window.location.origin;
    const narrativeId = selectedNarrativeId || "latest";
    return `${baseUrl}/share/${narrativeId}`;
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024 * 1024) {
      // Less than 1 MB, show in KB
      return `${(bytes / 1024).toFixed(1)} KB`;
    } else {
      // 1 MB or larger, show in MB
      return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    }
  };

  // Add this helper function near the other utility functions
  const safeCalculateChartValues = (data: Array<{ value: number }>) => {
    if (!data || data.length === 0) {
      return { min: 0, max: 100 };
    }

    const values = data.map((item) => item.value);
    return {
      min: Math.min(...values),
      max: Math.max(...values),
    };
  };

  return (
    <div className="container mx-auto">
      <PageHeader
        title=""
        parentPath="/deployed-projects"
        parentName="Deployed Projects"
        subViews={[{ label: currentViewInfo.label }]}
        currentSubView={currentViewInfo.label}
        defaultDescription={currentViewInfo.description}
        disableDropdown={true}
      />
      <div className="p-6">
        {!isDetailView ? (
          <div className="space-y-8">
            {/* Upload Section */}
            <div className="w-full">
              <div
                className={`flex flex-col items-start justify-center px-8 py-6 border-4 border-dashed rounded-xl h-fit transition-colors ${
                  isDragging ? "border-teal-500 bg-teal-50" : "border-gray-300"
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <h2 className="text-2xl text-slate-400 font-medium text-left mb-2">
                  Add a file to create a new Narrative{" "}
                </h2>
                <div className="flex flex-row items-center justify-between w-full h-[72px]">
                  <div className="flex flex-row items-center">
                    <input
                      type="file"
                      onChange={handleFileUpload}
                      className="hidden"
                      id="fileUpload"
                      ref={fileInputRef}
                      accept=".csv,.json,.xls,.xlsx"
                    />

                    {/* Upload Icon */}
                    {isUploading ? (
                      <LoadingSpinner size="small" />
                    ) : (
                      <svg
                        className="w-12 h-12 text-slate-400 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                        />
                      </svg>
                    )}

                    <div className="flex flex-col items-start">
                      <div className="flex items-center">
                        <p className="text-xl text-left font-bold text-slate-700">
                          {uploadedFile
                            ? uploadedFile.name
                            : "Drag & drop your file here"}
                        </p>
                        {uploadedFile && (
                          <button
                            onClick={handleClearFile}
                            className="flex flex-row items-center text-sm ml-2 border-2 border-red-500 rounded-full text-red-500 hover:text-red-700 transition p-1"
                            disabled={isUploading}
                          >
                            <svg
                              className="w-4 h-4"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M6 18L18 6M6 6l12 12"
                              />
                            </svg>
                          </button>
                        )}
                      </div>
                      <p className="text-md text-slate-400">
                        Supported formats: CSV, JSON, XLS, XLSX
                      </p>
                      {uploadError && (
                        <p className="text-sm text-red-500 mt-1">
                          {uploadError}
                        </p>
                      )}
                    </div>

                    {!uploadedFile && (
                      <>
                        <div className="flex flex-col items-center text-base text-slate-400 mx-8">
                          <span>|</span>
                          <p className=" font-medium">or</p>
                          <span>|</span>
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={handleBrowseClick}
                            className="bg-teal-500 text-white px-4 py-2 rounded-md hover:bg-teal-600 transition"
                            disabled={isUploading}
                          >
                            Browse Files
                          </button>
                          <button
                            onClick={handleSampleFile}
                            className="bg-white text-blue-500 px-4 py-2 rounded-md hover:underline transition"
                            disabled={isUploading}
                          >
                            Try Sample File
                          </button>
                        </div>
                      </>
                    )}
                  </div>
                  {/* Right Column - Create New Narrative button */}
                  <div className="flex flex-col justify-center items-center">
                    {uploadedFile && !isProcessing && uploadedFilePath && (
                      <button
                        onClick={handleCreateNarrative}
                        className="w-full max-w-xs py-3 px-6 rounded-md text-white font-medium transition bg-teal-500 hover:bg-teal-600"
                      >
                        + Create New Narrative
                      </button>
                    )}

                    {isProcessing && (
                      <div className="w-full max-w-xs mt-4">
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div
                            className="bg-teal-500 h-2.5 rounded-full transition-all duration-300"
                            style={{ width: `${uploadProgress}%` }}
                          ></div>
                        </div>
                        <p className="text-sm text-gray-500 mt-2 text-center">
                          Processing... {uploadProgress}%
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            {/* List of Narratives */}
            <div className="space-y-4">
              <div>
                <h2 className="text-xl font-bold mb-1">Your Narratives</h2>
                <p className="text-gray-500 mb-4">
                  Your previously created narratives will appear here.
                </p>
              </div>

              {/* Narrative Table */}
              {isLoadingNarratives ? (
                <div className="flex justify-center py-8">
                  <LoadingSpinner message="Loading narratives..." />
                </div>
              ) : (
                <DataNarrativeTable
                  data={sortedNarratives}
                  onSort={handleSort}
                  sortColumn={sortColumn}
                  sortDirection={sortDirection}
                  onViewNarrative={handleViewNarrative}
                />
              )}
            </div>{" "}
          </div>
        ) : (
          <div className="space-y-8">
            <div className="flex justify-between items-center mb-0">
              <div className="flex flex-col items-start">
                {/* Back button */}
                <button
                  onClick={handleBackToList}
                  className="flex text-left mb-2 text-slate-600 hover:text-slate-800 transition"
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M15 19l-7-7 7-7"
                    />
                  </svg>
                  Back to Narratives
                </button>
                <h1 className="text-3xl font-bold">
                  {selectedNarrative?.fileName}
                </h1>
              </div>
              {/* Download and Share buttons */}
              <div className="flex justify-end gap-4 mt-4">
                <button
                  onClick={handleDownload}
                  className="flex flex-row items-center border-teal-500 border-2 text-teal-500 px-6 py-2 rounded-md hover:bg-teal-600 hover:text-white"
                >
                  <TbDownload className="mr-2 h-5 w-5" />
                  Download Report
                </button>
                <button
                  onClick={handleShare}
                  className="flex flex-row items-center border-teal-500 border-2 text-teal-500 px-6 py-2 rounded-md hover:bg-teal-600 hover:text-white"
                >
                  <TbShare3 className="mr-2 h-5 w-5" />
                  Share Report
                </button>
              </div>
            </div>

            {/* AI Generated Summary - Full Width */}
            <section className="p-6 border rounded-lg">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold">Summary</h2>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">Tone:</span>
                    <select
                      value={summaryTone}
                      onChange={(e) =>
                        handleToneChange(e.target.value as SummaryTone)
                      }
                      className="border rounded-md px-2 py-1 text-sm"
                      disabled={
                        isGeneratingNarrative || isLoadingNarrativeDetail
                      }
                    >
                      <option value="formal">Formal</option>
                      <option value="conversational">Conversational</option>
                      <option value="analytical">Analytical</option>
                    </select>
                  </div>
                  <button
                    onClick={handleRegenerateSummary}
                    className="flex items-center text-teal-600 hover:text-teal-800"
                    disabled={isGeneratingNarrative || isLoadingNarrativeDetail}
                  >
                    <svg
                      className="w-4 h-4 mr-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                      />
                    </svg>
                    Regenerate
                  </button>
                </div>
              </div>
              <div className="bg-gray-50 p-4 rounded-md">
                {isGeneratingNarrative || isLoadingNarrativeDetail ? (
                  <div className="flex justify-center py-8">
                    <LoadingSpinner message="Generating narrative..." />
                  </div>
                ) : (
                  <NarrativeDisplay narrative={aiSummary} />
                )}
              </div>
            </section>

            {/* Data Summary and Preview - Split Row */}
            <div className="flex flex-col md:flex-row gap-6">
              {/* Data Preview - 2/3 Width */}
              <section className="p-6 border rounded-lg md:w-2/3">
                <h2 className="text-xl font-bold mb-4">Data Preview</h2>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        {sampleData.length > 0 &&
                          Object.keys(sampleData[0]).map((header) => (
                            <th
                              key={header}
                              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                            >
                              {header}
                            </th>
                          ))}
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {sampleData.map((row, rowIndex) => (
                        <tr key={rowIndex}>
                          {Object.values(row).map((value, cellIndex) => (
                            <td
                              key={cellIndex}
                              className="px-6 py-4 whitespace-nowrap text-sm text-gray-500"
                            >
                              {String(value)}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </section>

              {/* Data Summary - 1/3 Width */}
              <section className="p-6 border rounded-lg md:w-1/3">
                <h2 className="text-xl font-bold mb-4">Data Summary</h2>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">File Name:</span>
                    <span className="font-medium">
                      {selectedNarrative?.fileName || uploadedFile?.name}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">File Type:</span>
                    <span className="font-medium">
                      {selectedNarrative?.fileType || uploadedFile?.type}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Created:</span>
                    <span className="font-medium">
                      {selectedNarrative?.createdAt ||
                        new Date().toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Size:</span>
                    <span className="font-medium">
                      {selectedNarrative?.size ||
                        (uploadedFile ? formatFileSize(uploadedFile.size) : "")}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Rows:</span>
                    <span className="font-medium">{rowCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Columns:</span>
                    <span className="font-medium">{columnInfo.count}</span>
                  </div>
                  <div className="mt-4">
                    <h3 className="text-md font-semibold mb-2">
                      Column Types:
                    </h3>
                    <div className="space-y-1">
                      {Object.entries(columnInfo.types).map(
                        ([column, type]) => (
                          <div
                            key={column}
                            className="flex justify-between text-sm"
                          >
                            <span className="text-gray-600">{column}:</span>
                            <span className="font-medium">{type}</span>
                          </div>
                        )
                      )}
                    </div>
                  </div>
                </div>
              </section>
            </div>
            {/* Charts - Two Half-Width Sections */}
            <div className="flex flex-col md:flex-row gap-6">
              {/* Chart 1 - Numeric Data Visualization (Line Chart) */}
              <section className="p-6 border rounded-lg md:w-1/2">
  <div className="flex justify-between items-center mb-4">
    <h2 className="text-xl font-bold">Numeric Data Trend</h2>
    {numericColumns.length > 0 && (
      <div className="flex items-center space-x-2">
        <span className="text-sm text-gray-600">Column:</span>
        <select
          value={selectedNumericColumn || ""}
          onChange={(e) => handleNumericColumnChange(e.target.value)}
          className="border rounded-md px-2 py-1 text-sm"
        >
          {numericColumns.map((column) => (
            <option key={column} value={column}>
              {column}
            </option>
          ))}
        </select>
      </div>
    )}
  </div>

  <div className="h-64 bg-white p-4 relative">
    {isChartLoading ? (
      <div className="flex items-center justify-center h-full">
        <LoadingSpinner size="small" message="Updating chart..." />
      </div>
    ) : salesData.length > 0 ? (
      <div className="relative h-full">
        {/* Y-axis labels */}
        <div className="absolute left-0 top-0 bottom-0 w-12 flex flex-col justify-between text-xs text-gray-500">
          {Array.from({ length: 5 }).map((_, i) => {
            const value =
              Math.min(...salesData.map((d) => d.value)) +
              ((Math.max(...salesData.map((d) => d.value)) -
                Math.min(...salesData.map((d) => d.value))) /
                4) *
                (4 - i);
            return <span key={i}>{value.toFixed(0)}</span>;
          })}
        </div>

        {/* Chart container */}
        <div className="ml-12 h-full flex flex-col relative">
          {/* Grid lines */}
          {Array.from({ length: 5 }).map((_, i) => (
            <div
              key={i}
              className="absolute left-0 right-0 border-t border-gray-200"
              style={{ top: `${(i / 4) * 100}%` }}
            ></div>
          ))}

          {/* SVG Line Chart */}
          <svg className="absolute inset-0 w-full h-full" viewBox="0 0 100 100">
            <polyline
              points={salesData
                .map((item, index) => {
                  const x = (index / (salesData.length - 1)) * 100;
                  const minValue = Math.min(...salesData.map((d) => d.value));
                  const maxValue = Math.max(...salesData.map((d) => d.value));
                  const range = maxValue - minValue || 1; // Avoid division by zero
                  const y = ((maxValue - item.value) / range) * 90 + 5; // Invert Y-axis
                  return `${x},${y}`;
                })
                .join(" ")}
              fill="none"
              stroke="#14b8a6"
              strokeWidth="2"
              vectorEffect="non-scaling-stroke"
            />

            {/* Data Points */}
            {salesData.map((item, index) => {
              const x = (index / (salesData.length - 1)) * 100;
              const minValue = Math.min(...salesData.map((d) => d.value));
              const maxValue = Math.max(...salesData.map((d) => d.value));
              const range = maxValue - minValue || 1;
              const y = ((maxValue - item.value) / range) * 90 + 5; // Invert Y-axis
              return (
                <circle
                  key={index}
                  cx={`${x}%`}
                  cy={`${y}%`}
                  r="3"
                  fill="white"
                  stroke="#14b8a6"
                  strokeWidth="2"
                />
              );
            })}
          </svg>
        </div>

        {/* X-axis labels */}
        <div className="h-6 flex justify-between text-xs text-gray-500 pt-1">
          {salesData.map((item, index) => (
            <span key={index} className="text-center w-10 truncate">
              {item.label}
            </span>
          ))}
        </div>
      </div>
    ) : (
      <div className="flex items-center justify-center h-full text-gray-400">
        No data available for the selected column
      </div>
    )}
  </div>

  <div className="mt-2 text-sm text-gray-500 text-center">
    {selectedNumericColumn
      ? `Trend of ${selectedNumericColumn} values`
      : "Select a numeric column to view trend"}
  </div>
</section>

              {/* Chart 2 - Categorical Data Visualization (Pie Chart) */}
              <section className="p-6 border rounded-lg md:w-1/2">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-bold">
                    Categorical Distribution
                  </h2>
                  {categoricalColumns.length > 0 && (
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">Column:</span>
                      <select
                        value={selectedCategoricalColumn || ""}
                        onChange={(e) =>
                          handleCategoricalColumnChange(e.target.value)
                        }
                        className="border rounded-md px-2 py-1 text-sm"
                      >
                        {categoricalColumns.map((column) => (
                          <option key={column} value={column}>
                            {column}
                          </option>
                        ))}
                      </select>
                    </div>
                  )}
                </div>
                <div
                  className="h-64 bg-white p-4"
                  ref={(el) => (chartRefs.current[1] = el)}
                >
                  {isChartLoading ? (
                    <div className="flex items-center justify-center h-full">
                      <LoadingSpinner
                        size="small"
                        message="Updating chart..."
                      />
                    </div>
                  ) : categoryData.length > 0 ? (
                    <PieChart data={categoryData} width={400} height={250} />
                  ) : (
                    <div className="flex items-center justify-center h-full text-gray-400">
                      No categorical data available for selected column
                    </div>
                  )}
                </div>
                <div className="mt-2 text-sm text-gray-500 text-center">
                  {selectedCategoricalColumn
                    ? `Distribution of ${selectedCategoricalColumn}`
                    : "Select a categorical column to view distribution"}
                </div>
              </section>
            </div>

            {/* Detected Trends Section */}
            <section className="p-6 border rounded-lg">
              <h2 className="text-xl font-bold mb-4">Detected Trends</h2>
              <div className="space-y-4">
                {detectedTrends.length > 0 ? (
                  <ul className="space-y-2">
                    {detectedTrends.map((trend, index) => (
                      <li key={index} className="flex items-start">
                        <svg
                          className="w-5 h-5 text-teal-500 mr-2 mt-0.5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M9 5l7 7-7 7"
                          />
                        </svg>
                        <span className="text-gray-700">{trend}</span>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-gray-500">
                    No trends detected in this dataset.
                  </p>
                )}
              </div>
            </section>
          </div>
        )}
      </div>

      {/* Share Modal */}
      <ShareModal
        isOpen={isShareModalOpen}
        onClose={() => setIsShareModalOpen(false)}
        title={selectedNarrative?.fileName || "Data Narrative"}
        shareUrl={getShareUrl()}
      />
    </div>
  );
}
