## Components

This project includes several reusable UI components built with React and TypeScript. Here's a list of available components:

### Button

A customizable button component with loading state support.

```tsx
<Button
  title="Submit"
  loading={false}
  loadingText="Loading..."
  height={45}
  width={200}
  onClick={() => {}}
>
  Submit
</Button>
```

Props:

- `title`: Button text
- `loading`: <PERSON><PERSON>an to show loading state
- `loadingText`: Text to show during loading state
- `height`: Button height in pixels
- `width`: Button width in pixels (optional)
- `onClick`: Click handler function

### Checkbox

A simple checkbox component with custom styling.

```tsx
<Checkbox id="terms" checked={false} onChange={(e) => {}} />
```

Props:

- `id`: Unique identifier for the checkbox
- `checked`: <PERSON><PERSON>an for checked state
- `onChange`: Change handler function

### CheckboxWithLabel

A checkbox component with an associated label.

```tsx
<CheckboxWithLabel
  id="terms"
  label="I agree to the terms"
  checked={false}
  onChange={(e) => {}}
/>
```

Props:

- `id`: Unique identifier for the checkbox
- `label`: Text label for the checkbox
- `checked`: Boolean for checked state
- `onChange`: Change handler function

### Input

A customizable input component with support for various input types and styling options.

```tsx
<Input
  label="Email"
  value={email}
  onChange={(value) => {}}
  type="email"
  placeholder="Enter your email"
  required
  showInfo
  infoText="We'll never share your email"
/>
```

Props:

- Required:

  - `value`: Input value
  - `onChange`: Change handler function
  - `label`: Input label

- Optional:
  - `type`: Input type ('text', 'password', 'email', 'number', 'tel', 'url')
  - `placeholder`: Placeholder text
  - `required`: Boolean for required field
  - `disabled`: Boolean for disabled state
  - `showInfo`: Boolean to show info icon
  - `infoText`: Text for info tooltip
  - `error`: Boolean for error state
  - `errorMessage`: Error message text
  - Various styling props for customization

All components are styled using Tailwind CSS and include responsive design considerations.
