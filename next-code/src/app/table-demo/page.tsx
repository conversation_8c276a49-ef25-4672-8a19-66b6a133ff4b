'use client'

import { useState, useMemo } from 'react'
import { createColumnHelper, ColumnDef } from '@tanstack/react-table'
import { TableWithColumnManagement, getStatusBadgeClasses, createSelectionColumn, BulkAction } from '../components/table'
import { EditableSelectCell, createStatusBadgeDisplay, COMMON_STATUS_OPTIONS } from '../components/table/EditableSelectCell'

// Role options specific to this demo
const ROLE_OPTIONS = [
  { value: 'Senior Developer', label: 'Senior Developer' },
  { value: 'Product Manager', label: 'Product Manager' },
  { value: 'Designer', label: 'Designer' },
  { value: 'Senior Engineer', label: 'Senior Engineer' },
  { value: 'Marketing Lead', label: 'Marketing Lead' },
  { value: 'Data Analyst', label: 'Data Analyst' },
  { value: 'DevOps Engineer', label: 'DevOps Engineer' },
  { value: 'UX Designer', label: 'UX Designer' },
  { value: 'Junior Developer', label: '<PERSON> Developer' },
  { value: 'Backend Engineer', label: 'Backend Engineer' },
  { value: 'Frontend Engineer', label: 'Frontend Engineer' },
]

interface DemoData {
  id: string
  name: string
  email: string
  role: string
  department: string
  status: 'active' | 'inactive' | 'pending'
  joinDate: string
  salary: number
  projects: number
}

const columnHelper = createColumnHelper<DemoData>()

const sampleData: DemoData[] = [
  {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    role: 'Senior Developer',
    department: 'Engineering',
    status: 'active',
    joinDate: '2022-01-15',
    salary: 95000,
    projects: 8
  },
  {
    id: '2',
    name: 'Jane Smith',
    email: '<EMAIL>',
    role: 'Product Manager',
    department: 'Product',
    status: 'active',
    joinDate: '2021-08-20',
    salary: 105000,
    projects: 12
  },
  {
    id: '3',
    name: 'Mike Johnson',
    email: '<EMAIL>',
    role: 'Designer',
    department: 'Design',
    status: 'pending',
    joinDate: '2023-03-10',
    salary: 80000,
    projects: 3
  },
  {
    id: '4',
    name: 'Sarah Wilson',
    email: '<EMAIL>',
    role: 'Senior Engineer',
    department: 'Engineering',
    status: 'active',
    joinDate: '2020-11-05',
    salary: 110000,
    projects: 15
  },
  {
    id: '5',
    name: 'Tom Brown',
    email: '<EMAIL>',
    role: 'Marketing Lead',
    department: 'Marketing',
    status: 'inactive',
    joinDate: '2019-07-12',
    salary: 85000,
    projects: 6
  },
  {
    id: '6',
    name: 'Lisa Davis',
    email: '<EMAIL>',
    role: 'Data Analyst',
    department: 'Analytics',
    status: 'active',
    joinDate: '2022-09-18',
    salary: 75000,
    projects: 9
  },
  {
    id: '7',
    name: 'Chris Lee',
    email: '<EMAIL>',
    role: 'DevOps Engineer',
    department: 'Engineering',
    status: 'active',
    joinDate: '2021-12-03',
    salary: 98000,
    projects: 11
  },
  {
    id: '8',
    name: 'Emma Garcia',
    email: '<EMAIL>',
    role: 'UX Designer',
    department: 'Design',
    status: 'pending',
    joinDate: '2023-01-25',
    salary: 82000,
    projects: 4
  }
]

export default function TableDemo() {
  const [data, setData] = useState(sampleData)

  // Handle updating data when cells are edited
  const handleCellSave = (rowId: string, field: keyof DemoData, newValue: any) => {
    setData(prevData => 
      prevData.map(row => 
        row.id === rowId ? { ...row, [field]: newValue } : row
      )
    )
  }

  // Bulk actions for table demo
  const bulkActions: BulkAction<DemoData>[] = [
    {
      id: 'export',
      label: 'Export',
      variant: 'secondary',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      onClick: (selectedRows) => {
        console.log('Exporting:', selectedRows);
        alert(`Exported ${selectedRows.length} records to CSV`);
      }
    },
    {
      id: 'set-pending',
      label: 'Set Pending',
      variant: 'primary',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      onClick: (selectedRows) => {
        const updatedData = data.map(row => 
          selectedRows.some(selected => selected.id === row.id) 
            ? { ...row, status: 'pending' as const }
            : row
        );
        setData(updatedData);
        alert(`Set ${selectedRows.length} user(s) to pending status`);
      },
      disabled: (selectedRows) => selectedRows.every(row => row.status === 'pending')
    },
    {
      id: 'archive',
      label: 'Archive',
      variant: 'danger',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 8l6 6 6-6" />
        </svg>
      ),
      onClick: (selectedRows) => {
        if (confirm(`Archive ${selectedRows.length} user(s)?`)) {
          console.log('Archiving:', selectedRows);
          alert(`Archived ${selectedRows.length} user(s)`);
        }
      }
    }
  ];

  const columns = useMemo(
    () => [
      createSelectionColumn<DemoData>(),
      columnHelper.accessor('name', {
        header: 'Name',
        cell: info => (
          <div className="text-left">
            <div className="font-medium text-gray-900">{info.getValue()}</div>
            <div className="text-sm text-gray-500">{info.row.original.email}</div>
          </div>
        ),
      }),
      columnHelper.accessor('role', {
        header: 'Role',
        cell: info => (
          <div className="text-left">
            <EditableSelectCell
              value={info.getValue()}
              options={ROLE_OPTIONS}
              onSave={(newValue) => {
                handleCellSave(info.row.id, 'role', newValue)
              }}
              searchable={true}
              placeholder="Select role..."
              renderDisplay={(value) => (
                <div>
                  <div className="font-medium">{value}</div>
                  <div className="text-sm text-gray-500">{info.row.original.department}</div>
                </div>
              )}
            />
          </div>
        ),
      }),
      columnHelper.accessor('status', {
        header: 'Status',
        cell: info => (
          <EditableSelectCell
            value={info.getValue()}
            options={COMMON_STATUS_OPTIONS}
            onSave={(newValue) => {
              handleCellSave(info.row.id, 'status', newValue as 'active' | 'inactive' | 'pending')
            }}
            renderDisplay={createStatusBadgeDisplay(getStatusBadgeClasses)}
            placeholder="Select status..."
            searchable={true}
          />
        ),
        filterFn: 'equalsString',
      }),
      columnHelper.accessor('joinDate', {
        header: 'Join Date',
        cell: info => new Date(info.getValue()).toLocaleDateString(),
      }),
      columnHelper.accessor('salary', {
        header: 'Salary',
        cell: info => `$${info.getValue().toLocaleString()}`,
      }),
      columnHelper.accessor('projects', {
        header: 'Projects',
        cell: info => (
          <div className="flex items-center justify-center">
            <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-sm font-medium">
              {info.getValue()}
            </span>
          </div>
        ),
      }),
    ],
    []
  )

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Table Component Demo</h1>
          <p className="mt-2 text-gray-600">
            Comprehensive TanStack Table v8 integration with all features enabled
          </p>
          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">✨ New Features</h3>
            <ul className="text-blue-800 text-sm space-y-1">
              <li>• <strong>Editable Cells</strong>: Click on Role or Status cells to edit with dropdown selects</li>
              <li>• <strong>Density Toggle</strong>: Switch between normal and condensed views for better space utilization</li>
              <li>• <strong>Bulk Actions</strong>: Select multiple rows using checkboxes for bulk operations</li>
              <li>• <strong>Auto-save</strong>: Changes save automatically on selection</li>
              <li>• <strong>Searchable Options</strong>: Type to filter dropdown options</li>
            </ul>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <TableWithColumnManagement
            data={data}
            columns={columns as ColumnDef<DemoData>[]}
            features={{
              sorting: true,
              filtering: true,
              globalFilter: true,
              pagination: true,
              grouping: true,
              expanding: true,
              columnResizing: true,
              columnOrdering: true,
              columnPinning: true,
              rowPinning: true,
              rowSelection: true,
            }}
            initialState={{
              pagination: { pageIndex: 0, pageSize: 5 },
              sorting: [{ id: 'name', desc: false }],
            }}
            showDensityToggle={true}
            bulkActions={bulkActions}
            showBulkActions={true}
            className="w-full"
          />
        </div>

      </div>
    </div>
  )
}