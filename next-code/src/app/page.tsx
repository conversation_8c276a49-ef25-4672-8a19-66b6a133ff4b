"use client";

import {
  <PERSON><PERSON>,
  <PERSON>ggle,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  PageHeader,
} from "./components";
import Checkbox from "./components/checkbox";
import { useState } from "react";
import CheckboxWithLabel from "./components/checkbox-with-label";
import { Input } from "./components/custom-input";
import { Upload } from "./components/upload";
import { Select } from "./components/select";
import {
  PrimaryTabs,
  SecondaryTabs,
  SimpleTabs,
} from "./components/tabs";
import {
  TableWithColumnManagement,
  getStatusBadgeClasses,
  EditableSelectCell,
  createStatusBadgeDisplay,
  COMMON_STATUS_OPTIONS,
  createSelectionColumn,
  BulkAction,
} from "./components/table";
import { createColumnHelper, ColumnDef } from "@tanstack/react-table";
import { useMemo } from "react";

interface DemoData {
  id: string;
  name: string;
  email: string;
  role: string;
  department: string;
  status: "active" | "inactive" | "pending";
  joinDate: string;
  salary: number;
  projects: number;
}

const columnHelper = createColumnHelper<DemoData>();

// Role options for editable dropdown
const ROLE_OPTIONS = [
  { value: 'Senior Developer', label: 'Senior Developer' },
  { value: 'Product Manager', label: 'Product Manager' },
  { value: 'Designer', label: 'Designer' },
  { value: 'Senior Engineer', label: 'Senior Engineer' },
  { value: 'Marketing Lead', label: 'Marketing Lead' },
  { value: 'Data Analyst', label: 'Data Analyst' },
  { value: 'DevOps Engineer', label: 'DevOps Engineer' },
  { value: 'UX Designer', label: 'UX Designer' },
  { value: 'Junior Developer', label: 'Junior Developer' },
  { value: 'Backend Engineer', label: 'Backend Engineer' },
  { value: 'Frontend Engineer', label: 'Frontend Engineer' },
];

// Select options data
const countryOptions = [
  { value: "us", label: "United States" },
  { value: "ca", label: "Canada" },
  { value: "uk", label: "United Kingdom" },
  { value: "de", label: "Germany" },
  { value: "fr", label: "France" },
  { value: "jp", label: "Japan" },
  { value: "au", label: "Australia" },
  { value: "br", label: "Brazil" },
  { value: "in", label: "India" },
  { value: "ch", label: "China" },
];

const frameworkOptions = [
  { value: "react", label: "React" },
  { value: "vue", label: "Vue.js" },
  { value: "angular", label: "Angular" },
  { value: "svelte", label: "Svelte" },
  { value: "nextjs", label: "Next.js" },
  { value: "nuxt", label: "Nuxt.js" },
  { value: "remix", label: "Remix" },
  { value: "gatsby", label: "Gatsby" },
];

const priorityOptions = [
  { value: "low", label: "Low Priority" },
  { value: "medium", label: "Medium Priority" },
  { value: "high", label: "High Priority" },
  { value: "urgent", label: "Urgent" },
  { value: "critical", label: "Critical", disabled: true },
];

const sampleTableData: DemoData[] = [
  {
    id: "1",
    name: "John Doe",
    email: "<EMAIL>",
    role: "Senior Developer",
    department: "Engineering",
    status: "active",
    joinDate: "2022-01-15",
    salary: 95000,
    projects: 8,
  },
  {
    id: "2",
    name: "Jane Smith",
    email: "<EMAIL>",
    role: "Product Manager",
    department: "Product",
    status: "active",
    joinDate: "2021-08-20",
    salary: 105000,
    projects: 12,
  },
  {
    id: "3",
    name: "Mike Johnson",
    email: "<EMAIL>",
    role: "Designer",
    department: "Design",
    status: "pending",
    joinDate: "2023-03-10",
    salary: 80000,
    projects: 3,
  },
  {
    id: "4",
    name: "Sarah Wilson",
    email: "<EMAIL>",
    role: "Senior Engineer",
    department: "Engineering",
    status: "active",
    joinDate: "2020-11-05",
    salary: 110000,
    projects: 15,
  },
  {
    id: "5",
    name: "Tom Brown",
    email: "<EMAIL>",
    role: "Marketing Lead",
    department: "Marketing",
    status: "inactive",
    joinDate: "2019-07-12",
    salary: 85000,
    projects: 6,
  },
  {
    id: "6",
    name: "Lisa Davis",
    email: "<EMAIL>",
    role: "Data Analyst",
    department: "Analytics",
    status: "active",
    joinDate: "2022-09-18",
    salary: 75000,
    projects: 9,
  },
];

export default function Home() {
  const [checkboxActive, setCheckboxActive] = useState(true);
  const [checkboxWithLableActive, setCheckboxWithLableActive] = useState(true);
  const [toggleActive, setToggleActive] = useState(false);
  const [selectedRadio, setSelectedRadio] = useState("option1");
  const [tags, setTags] = useState([
    { id: 1, label: "React", variant: "primary" as const },
    { id: 2, label: "TypeScript", variant: "secondary" as const },
    { id: 3, label: "Tailwind", variant: "success" as const },
  ]);
  const [password, setPassword] = useState("");
  const [email, setEmail] = useState("");
  const [age, setAge] = useState("");
  const [name, setName] = useState("");
  const [projectName, setProjectName] = useState("");
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");
  const [tableData, setTableData] = useState(sampleTableData);
  const [selectedCountry, setSelectedCountry] = useState("");
  const [selectedFramework, setSelectedFramework] = useState("react");
  const [selectedPriority, setSelectedPriority] = useState("");

  // Handle updating table data when cells are edited
  const handleCellSave = (rowId: string, field: keyof DemoData, newValue: any) => {
    setTableData(prevData =>
      prevData.map(row =>
        row.id === rowId ? { ...row, [field]: newValue } : row
      )
    );
  };

  // Bulk actions configuration
  const bulkActions: BulkAction<DemoData>[] = [
    {
      id: 'activate',
      label: 'Activate',
      variant: 'primary',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
        </svg>
      ),
      onClick: (selectedRows) => {
        const updatedData = tableData.map(row => 
          selectedRows.some(selected => selected.id === row.id) 
            ? { ...row, status: 'active' as const }
            : row
        );
        setTableData(updatedData);
        alert(`Activated ${selectedRows.length} user(s)`);
      },
      disabled: (selectedRows) => selectedRows.every(row => row.status === 'active')
    },
    {
      id: 'deactivate',
      label: 'Deactivate',
      variant: 'secondary',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
        </svg>
      ),
      onClick: (selectedRows) => {
        const updatedData = tableData.map(row => 
          selectedRows.some(selected => selected.id === row.id) 
            ? { ...row, status: 'inactive' as const }
            : row
        );
        setTableData(updatedData);
        alert(`Deactivated ${selectedRows.length} user(s)`);
      },
      disabled: (selectedRows) => selectedRows.every(row => row.status === 'inactive')
    },
    {
      id: 'delete',
      label: 'Delete',
      variant: 'danger',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
      ),
      onClick: (selectedRows) => {
        if (confirm(`Are you sure you want to delete ${selectedRows.length} user(s)?`)) {
          const updatedData = tableData.filter(row => 
            !selectedRows.some(selected => selected.id === row.id)
          );
          setTableData(updatedData);
          alert(`Deleted ${selectedRows.length} user(s)`);
        }
      }
    }
  ];

  const handleFileUpload = (file: File) => {
    setIsUploading(true);
    setUploadedFile(file);

    // Simulate upload process
    setTimeout(() => {
      setIsUploading(false);
      console.log("File uploaded:", file.name);
    }, 2000);
  };

  const handleClearFile = () => {
    setUploadedFile(null);
  };

  const sampleData = `id,name,sales,region,date
1,Product A,1250,North,2023-01-15
2,Product B,890,South,2023-01-16
3,Product C,1400,East,2023-01-17
4,Product D,950,West,2023-01-18`;

  // Table columns definition
  const columns = useMemo(
    (): ColumnDef<DemoData>[] => [
      createSelectionColumn<DemoData>(),
      columnHelper.accessor("name", {
        header: "Name",
        cell: (info) => (
          <div className="text-left">
            <div className="font-medium text-gray-900">{info.getValue()}</div>
            <div className="text-sm text-gray-500">
              {info.row.original.email}
            </div>
          </div>
        ),
      }),
      columnHelper.accessor("role", {
        header: "Role",
        cell: (info) => (
          <div className="text-left">
            <EditableSelectCell
              value={info.getValue()}
              options={ROLE_OPTIONS}
              onSave={(newValue) => {
                handleCellSave(info.row.id, 'role', newValue)
              }}
              searchable={true}
              placeholder="Select role..."
              renderDisplay={(value) => (
                <div>
                  <div className="font-medium">{value}</div>
                  <div className="text-sm text-gray-500">{info.row.original.department}</div>
                </div>
              )}
            />
          </div>
        ),
      }),
      columnHelper.accessor("status", {
        header: "Status",
        cell: (info) => (
          <EditableSelectCell
            value={info.getValue()}
            options={COMMON_STATUS_OPTIONS}
            onSave={(newValue) => {
              handleCellSave(info.row.id, 'status', newValue as 'active' | 'inactive' | 'pending')
            }}
            renderDisplay={createStatusBadgeDisplay(getStatusBadgeClasses)}
            placeholder="Select status..."
            searchable={true}
          />
        ),
        filterFn: "equalsString",
      }),
      columnHelper.accessor("joinDate", {
        header: "Join Date",
        cell: (info) => new Date(info.getValue()).toLocaleDateString(),
      }),
      columnHelper.accessor("salary", {
        header: "Salary",
        cell: (info) => `$${info.getValue().toLocaleString()}`,
      }),
      columnHelper.accessor("projects", {
        header: "Projects",
        cell: (info) => (
          <div className="flex items-center justify-center">
            <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-sm font-medium">
              {info.getValue()}
            </span>
          </div>
        ),
      }),
    ],
    []
  );

  // Sample tabs data
  const tabsData = [
    {
      value: "overview",
      label: "Overview",
      content: (
        <div className="p-4 border rounded-lg bg-gray-50">
          <h3 className="text-lg font-semibold mb-2">Overview Content</h3>
          <p className="text-gray-600">
            This is the overview tab content. It contains general information
            about the project or application.
          </p>
          <ul className="mt-4 space-y-2">
            <li>• Feature overview</li>
            <li>• Key metrics</li>
            <li>• Quick actions</li>
          </ul>
        </div>
      ),
    },
    {
      value: "analytics",
      label: "Analytics",
      content: (
        <div className="p-4 border rounded-lg bg-blue-50">
          <h3 className="text-lg font-semibold mb-2">Analytics Dashboard</h3>
          <p className="text-gray-600">
            View detailed analytics and performance metrics.
          </p>
          <div className="mt-4 grid grid-cols-3 gap-4">
            <div className="bg-white p-3 rounded border">
              <div className="text-2xl font-bold text-blue-600">1,234</div>
              <div className="text-sm text-gray-500">Total Views</div>
            </div>
            <div className="bg-white p-3 rounded border">
              <div className="text-2xl font-bold text-green-600">89%</div>
              <div className="text-sm text-gray-500">Success Rate</div>
            </div>
            <div className="bg-white p-3 rounded border">
              <div className="text-2xl font-bold text-purple-600">456</div>
              <div className="text-sm text-gray-500">Active Users</div>
            </div>
          </div>
        </div>
      ),
    },
    {
      value: "settings",
      label: "Settings",
      content: (
        <div className="p-4 border rounded-lg bg-green-50">
          <h3 className="text-lg font-semibold mb-2">Application Settings</h3>
          <p className="text-gray-600">
            Configure your application preferences and options.
          </p>
          <div className="mt-4 space-y-4">
            <div className="flex items-center justify-between">
              <span>Dark Mode</span>
              <input type="checkbox" className="toggle" />
            </div>
            <div className="flex items-center justify-between">
              <span>Notifications</span>
              <input type="checkbox" className="toggle" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <span>Auto-save</span>
              <input type="checkbox" className="toggle" defaultChecked />
            </div>
          </div>
        </div>
      ),
    },
    {
      value: "help",
      label: "Help",
      disabled: false,
      content: (
        <div className="p-4 border rounded-lg bg-yellow-50">
          <h3 className="text-lg font-semibold mb-2">Help & Support</h3>
          <p className="text-gray-600">
            Find answers to common questions and get support.
          </p>
          <div className="mt-4 space-y-2">
            <div className="p-3 bg-white rounded border">
              <strong>Q: How do I upload files?</strong>
              <p className="text-sm text-gray-600 mt-1">
                You can drag and drop files or click the browse button in the
                upload section.
              </p>
            </div>
            <div className="p-3 bg-white rounded border">
              <strong>Q: What file formats are supported?</strong>
              <p className="text-sm text-gray-600 mt-1">
                We support CSV, JSON, XLS, and XLSX file formats.
              </p>
            </div>
          </div>
        </div>
      ),
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Page Header Component Demo */}
      <PageHeader
        title="Sutra UI Component Library"
        description="Comprehensive design system and component showcase"
        breadcrumbs={[
          { label: "Home", href: "/" },
          { label: "Components", href: "/components" },
          { label: "Library" }
        ]}
        actions={[
          {
            label: "Export",
            onClick: () => alert("Export clicked!"),
            variant: "secondary"
          },
          {
            label: "Settings",
            onClick: () => alert("Settings clicked!"),
            variant: "ghost"
          }
        ]}
        onAboutClick={() => alert("About this component library")}
      />

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Basic Components Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          {/* Basic Interactive Components */}
          <div className="bg-white col-span-1 rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-800">
              Basic Components
            </h2>
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">
                  Buttons
                </h3>
                <div className="space-y-2 space-x-2">
                  <Button
                    onClick={() => alert("Primary clicked!")}
                    title="Primary"
                    variant="primary"
                    width={120}
                  >
                    Primary
                  </Button>
                  <Button
                    onClick={() => alert("Secondary clicked!")}
                    title="Secondary"
                    variant="secondary"
                    width={120}
                  >
                    Secondary
                  </Button>
                  <Button
                    onClick={() => alert("Tertiary clicked!")}
                    title="Tertiary"
                    variant="tertiary"
                    width={120}
                  >
                    Tertiary
                  </Button>
                  <Button
                    onClick={() => alert("Ghost clicked!")}
                    title="Ghost"
                    variant="ghost"
                    width={120}
                  >
                    Ghost
                  </Button>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">
                  Checkbox
                </h3>
                <Checkbox
                  id="dontShowAgain"
                  checked={checkboxActive}
                  onChange={() => setCheckboxActive(!checkboxActive)}
                />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">
                  Checkbox with Label
                </h3>
                <CheckboxWithLabel
                  id="checkboxWithLabel"
                  label="Enable notifications"
                  checked={checkboxWithLableActive}
                  onChange={() =>
                    setCheckboxWithLableActive(!checkboxWithLableActive)
                  }
                />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">
                  Toggle Switch
                </h3>
                <Toggle
                  id="toggleSwitch"
                  checked={toggleActive}
                  onChange={setToggleActive}
                  label="Dark mode"
                  size="md"
                />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">
                  Radio Buttons
                </h3>
                <Radio
                  name="sampleRadio"
                  options={[
                    { value: "option1", label: "Option 1" },
                    { value: "option2", label: "Option 2" },
                    { value: "option3", label: "Option 3" },
                  ]}
                  value={selectedRadio}
                  onChange={setSelectedRadio}
                  orientation="vertical"
                />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">
                  Tags/Chips
                </h3>
                <div className="flex flex-wrap gap-2">
                  {tags.map((tag) => (
                    <Tag
                      key={tag.id}
                      variant={tag.variant}
                      size="sm"
                      removable
                      onRemove={() =>
                        setTags(tags.filter((t) => t.id !== tag.id))
                      }
                    >
                      {tag.label}
                    </Tag>
                  ))}
                  <Tag variant="default" size="sm">
                    Small
                  </Tag>
                  <Tag variant="warning" size="sm">Warning</Tag>
                  <Tag
                    variant="success"
                    size="sm"
                    icon={
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                      </svg>
                    }
                  >
                    With Icon
                  </Tag>
                </div>
              </div>
            </div>
          </div>

          <div className="col-span-2 space-y-8">
            {/* Input Components - Column 1 */}
            <div className="bg-white col-span-2 rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-800">
                Input Components
              </h2>
              <div className="flex flex-col lg:flex-row gap-8">
                <div className="space-y-4 w-full">
                  <Input
                    value={projectName}
                    onChange={setProjectName}
                    label="Project Name"
                    placeholder="Enter project name"
                    type="text"
                    required
                    showInfo
                    infoText="Enter a clear project name"
                  />
                  <Input
                    value={name}
                    onChange={setName}
                    label="Full Name"
                    placeholder="Enter your name"
                    type="text"
                  />
                  <Input
                    value={email}
                    onChange={setEmail}
                    label="Email"
                    placeholder="Enter your email"
                    type="email"
                    required
                  />
                </div>
                <div className="space-y-4 w-full">
                  <Input
                    value={age}
                    onChange={setAge}
                    label="Age"
                    placeholder="Enter your age"
                    type="number"
                    minLength={1}
                    maxLength={3}
                  />
                  <Input
                    value={password}
                    onChange={setPassword}
                    label="Password"
                    placeholder="Enter your password"
                    type="password"
                  />
                </div>
              </div>
              
              {/* Select Components */}
              <div className="pt-6 border-t border-gray-200">
                <h3 className="text-lg font-medium mb-4 text-gray-700">Select Components</h3>
                <div className="flex flex-col lg:flex-row gap-8">
                  <div className="space-y-4 w-full">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Country (Searchable & Clearable)
                      </label>
                      <Select
                        value={selectedCountry}
                        onChange={setSelectedCountry}
                        options={countryOptions}
                        placeholder="Select a country..."
                        searchable
                        clearable
                        height={40}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Framework (Pre-selected)
                      </label>
                      <Select
                        value={selectedFramework}
                        onChange={setSelectedFramework}
                        options={frameworkOptions}
                        placeholder="Select a framework..."
                        height={40}
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-4 w-full">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Priority (With Disabled Option)
                      </label>
                      <Select
                        value={selectedPriority}
                        onChange={setSelectedPriority}
                        options={priorityOptions}
                        placeholder="Select priority level..."
                        clearable
                        height={40}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Status (Error State)
                      </label>
                      <Select
                        value=""
                        onChange={() => {}}
                        options={[
                          { value: "active", label: "Active" },
                          { value: "inactive", label: "Inactive" },
                          { value: "pending", label: "Pending" }
                        ]}
                        placeholder="This field is required..."
                        error={true}
                        errorMessage="Please select a status"
                        height={40}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Card Components */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-800">
                Card Components
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Basic Card */}
                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold text-gray-900">
                      Basic Card
                    </h3>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">
                      This is a basic card with header and content sections.
                    </p>
                  </CardContent>
                </Card>

                {/* Card with Footer */}
                <Card hover>
                  <CardHeader>
                    <h3 className="text-lg font-semibold text-gray-900">
                      Interactive Card
                    </h3>
                    <p className="text-sm text-gray-500">
                      Hover effect enabled
                    </p>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">
                      This card has hover effects and includes a footer section.
                    </p>
                  </CardContent>
                  <CardFooter>
                    <div className="flex flex-col justify-between items-end space-y-2">
                      <Button title="View Details" variant="primary">
                        View Details
                      </Button>
                      <span className="text-xs text-gray-500">
                        Updated 2 hours ago
                      </span>
                    </div>
                  </CardFooter>
                </Card>

                {/* Styled Card */}
                <Card shadow="md" padding="lg">
                  <CardContent>
                    <div className="text-center">
                      <div className="w-12 h-12 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg
                          className="w-6 h-6 text-teal-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        Success Card
                      </h3>
                      <p className="text-gray-600">
                        Custom styling with icons and centered content.
                      </p>
                    </div>


                  </CardContent>
                </Card>
                {/* Image  Card */}
                <Card shadow="md" padding="lg">
                  <CardContent>
                    <div className="text-center">
                      <div className="w-full h-full rounded-full flex items-center justify-center mx-auto mb-7">
                        <img
                          src="https://sutra.ai/wp-content/uploads/2024/02/SUTRA-final-logo-01qw.png"
                          alt="SUTRA Logo"
                          className="w-full h-full object-contain"
                        />
                      </div>
                      <div className="flex justify-between">
                        <div>
                          <h3 className="text-md font-semibold text-left text-gray-900">
                            Image Card
                          </h3>
                          <p className="text-gray-600 text-left text-sm">
                            Subtitle
                          </p>
                        </div>
                        <div>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                            strokeWidth={2}
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" />
                          </svg>
                        </div>
                      </div>


                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>

        {/* Upload Component - Full Width */}
        <div className="bg-white rounded-lg shadow-sm  p-6 mb-12">
          <h2 className="text-2xl font-semibold mb-6 text-gray-800">
            Upload Component
          </h2>
          <Upload
            uploadedFile={uploadedFile}
            onFileUpload={handleFileUpload}
            onClearFile={handleClearFile}
            isUploading={isUploading}
            title="Upload your data file"
            description="Drag and drop or browse to upload CSV, JSON, XLS, or XLSX files"
            sampleFileData={sampleData}
            sampleFileName="sample-sales-data.csv"
            maxFileSize={10 * 1024 * 1024} // 10MB limit
          />
        </div>

        {/* Tabs Components - Full Width */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-12">
          <h2 className="text-2xl font-semibold mb-4 text-gray-800">
            Tab Components
          </h2>
          <p className="text-gray-600 mb-6">
            Flexible tab components with different styles - Primary (default), Secondary (underline), and Simple (customizable).
          </p>
          
          <div className="space-y-8">
            {/* Primary Tabs */}
            <div>
              <h3 className="text-lg font-medium mb-4 text-gray-700">Primary Tabs</h3>
              <PrimaryTabs 
                tabs={tabsData}
                defaultValue="overview"
                size="md"
              />
            </div>

            {/* Secondary Tabs */}
            <div>
              <h3 className="text-lg font-medium mb-4 text-gray-700">Secondary Tabs (Underline Style)</h3>
              <SecondaryTabs 
                tabs={tabsData}
                defaultValue="analytics"
                size="md"
              />
            </div>

            {/* Simple Tabs with Pills */}
            <div>
              <h3 className="text-lg font-medium mb-4 text-gray-700">Simple Tabs (Pills Style)</h3>
              <SimpleTabs 
                tabs={tabsData}
                defaultValue="settings"
                variant="pills"
                size="sm"
              />
            </div>
          </div>
        </div>

        {/* Advanced Table Component - Full Width */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-2xl font-semibold mb-2 text-gray-800">
            Advanced Table Component
          </h2>
          <p className="text-gray-600 mb-6">
            Advanced data table with TanStack Table v8 integration, comprehensive features, 
            <strong> editable cells with dropdown functionality</strong>, <strong>condensed view toggle</strong>, and <strong>bulk actions with checkboxes</strong>. 
            Click on Role or Status cells to edit them, use the density toggle to switch views, or select multiple rows for bulk operations!
          </p>


          {/* Table Component Demo - Full Width */}
          <div className=" p-6 mb-12">

            <div className="mb-6">
              <TableWithColumnManagement
                data={tableData}
                columns={columns as ColumnDef<DemoData>[]}
                features={{
                  sorting: true,
                  filtering: false,
                  globalFilter: false,
                  pagination: true,
                  grouping: true,
                  expanding: true,
                  columnResizing: true,
                  columnOrdering: true,
                  columnPinning: true,
                  rowPinning: true,
                  rowSelection: true,
                }}
                initialState={{
                  pagination: { pageIndex: 0, pageSize: 5 },
                  sorting: [{ id: "name", desc: false }],
                }}
                showDensityToggle={true}
                bulkActions={bulkActions}
                showBulkActions={true}
                className="w-full"
              />
            </div>

          </div>
        </div>
      </div>
    </div>
  );
}
