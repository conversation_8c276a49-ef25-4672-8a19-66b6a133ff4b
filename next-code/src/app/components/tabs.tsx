'use client';

import * as React from 'react';
import { useState, createContext, useContext } from 'react';

// Utility function for className merging (simple version)
const cn = (...classes: (string | undefined)[]) => {
  return classes.filter(Boolean).join(' ');
};

// Tab item interface
interface TabItem {
  value: string;
  label: React.ReactNode;
  content: React.ReactNode;
  disabled?: boolean;
}

// Context for managing tab state
interface TabsContextValue {
  activeTab: string;
  setActiveTab: (value: string) => void;
  orientation?: 'horizontal' | 'vertical';
}

const TabsContext = createContext<TabsContextValue | null>(null);

const useTabsContext = () => {
  const context = useContext(TabsContext);
  if (!context) {
    throw new Error('Tab components must be used within a Tabs component');
  }
  return context;
};

// Tabs Root Component
interface TabsProps {
  defaultValue?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  orientation?: 'horizontal' | 'vertical';
  className?: string;
  children: React.ReactNode;
}

export const Tabs = ({ 
  defaultValue, 
  value, 
  onValueChange, 
  orientation = 'horizontal',
  className = '',
  children 
}: TabsProps) => {
  const [internalValue, setInternalValue] = useState(defaultValue || '');
  
  const activeTab = value !== undefined ? value : internalValue;
  
  const handleTabChange = (newValue: string) => {
    if (value === undefined) {
      setInternalValue(newValue);
    }
    onValueChange?.(newValue);
  };

  return (
    <TabsContext.Provider value={{ 
      activeTab, 
      setActiveTab: handleTabChange,
      orientation 
    }}>
      <div className={cn('w-full', className)}>
        {children}
      </div>
    </TabsContext.Provider>
  );
};

// TabsList Component
interface TabsListProps {
  className?: string;
  children: React.ReactNode;
  variant?: 'default' | 'pills' | 'underline';
  size?: 'sm' | 'md' | 'lg';
}

export const TabsList = React.forwardRef<HTMLDivElement, TabsListProps>(
  ({ className = '', children, variant = 'default', size = 'md', ...props }, ref) => {
    const { orientation } = useTabsContext();
    
    const baseClasses = 'inline-flex items-center justify-center text-muted-foreground';
    const orientationClasses = orientation === 'vertical' ? 'flex-col' : '';
    
    const variantClasses = {
      default: 'rounded-lg bg-gray-100 p-1',
      pills: 'space-x-1',
      underline: 'border-b border-gray-200'
    }[variant];
    
    const sizeClasses = {
      sm: 'h-8',
      md: 'h-9', 
      lg: 'h-10'
    }[size];

    return (
      <div
        ref={ref}
        className={cn(baseClasses, orientationClasses, variantClasses, sizeClasses, className)}
        role="tablist"
        aria-orientation={orientation}
        {...props}
      >
        {children}
      </div>
    );
  }
);

TabsList.displayName = 'TabsList';

// TabsTrigger Component
interface TabsTriggerProps {
  value: string;
  className?: string;
  children: React.ReactNode;
  disabled?: boolean;
  variant?: 'default' | 'pills' | 'underline';
}

export const TabsTrigger = React.forwardRef<HTMLButtonElement, TabsTriggerProps>(
  ({ value, className = '', children, disabled = false, variant = 'default', ...props }, ref) => {
    const { activeTab, setActiveTab } = useTabsContext();
    const isActive = activeTab === value;

    const baseClasses = 'inline-flex items-center justify-center whitespace-nowrap px-3 py-1 text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-teal-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50';
    
    const variantClasses = {
      default: cn(
        'rounded-md',
        isActive 
          ? 'bg-gray-700 text-white shadow-sm' 
          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
      ),
      pills: cn(
        'rounded-full px-4',
        isActive 
          ? 'bg-teal-500 text-white shadow-sm' 
          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
      ),
      underline: cn(
        'rounded-none border-b-2 px-4 py-2',
        isActive 
          ? 'border-teal-500 text-teal-600' 
          : 'border-transparent text-gray-600 hover:text-gray-900 hover:border-gray-300'
      )
    }[variant];

    return (
      <button
        ref={ref}
        role="tab"
        aria-selected={isActive}
        aria-controls={`panel-${value}`}
        tabIndex={isActive ? 0 : -1}
        className={cn(baseClasses, variantClasses, className)}
        onClick={() => !disabled && setActiveTab(value)}
        disabled={disabled}
        {...props}
      >
        {children}
      </button>
    );
  }
);

TabsTrigger.displayName = 'TabsTrigger';

// TabsContent Component
interface TabsContentProps {
  value: string;
  className?: string;
  children: React.ReactNode;
  forceMount?: boolean;
}

export const TabsContent = React.forwardRef<HTMLDivElement, TabsContentProps>(
  ({ value, className = '', children, forceMount = false, ...props }, ref) => {
    const { activeTab } = useTabsContext();
    const isActive = activeTab === value;

    if (!isActive && !forceMount) {
      return null;
    }

    return (
      <div
        ref={ref}
        role="tabpanel"
        id={`panel-${value}`}
        aria-labelledby={`tab-${value}`}
        tabIndex={0}
        className={cn(
          'mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-teal-500 focus-visible:ring-offset-2',
          !isActive && forceMount && 'hidden',
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

TabsContent.displayName = 'TabsContent';

// Primary Tabs Component (Design System Primary)
// Use this for main navigation and primary content organization
interface PrimaryTabsProps {
  tabs: TabItem[];
  defaultValue?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  size?: 'sm' | 'md' | 'lg';
  orientation?: 'horizontal' | 'vertical';
  className?: string;
  contentClassName?: string;
}

export const PrimaryTabs = ({
  tabs,
  defaultValue,
  value,
  onValueChange,
  size = 'md',
  orientation = 'horizontal',
  className = '',
  contentClassName = ''
}: PrimaryTabsProps) => {
  const initialValue = value || defaultValue || tabs[0]?.value || '';

  return (
    <Tabs
      defaultValue={initialValue}
      value={value}
      onValueChange={onValueChange}
      orientation={orientation}
      className={className}
    >
      <TabsList variant="default" size={size}>
        {tabs.map((tab) => (
          <TabsTrigger 
            key={tab.value} 
            value={tab.value} 
            disabled={tab.disabled}
            variant="default"
          >
            {tab.label}
          </TabsTrigger>
        ))}
      </TabsList>
      
      {tabs.map((tab) => (
        <TabsContent 
          key={tab.value} 
          value={tab.value}
          className={contentClassName}
        >
          {tab.content}
        </TabsContent>
      ))}
    </Tabs>
  );
};

// Secondary Tabs Component (Design System Secondary)
// Use this for sub-navigation and secondary content organization
interface SecondaryTabsProps {
  tabs: TabItem[];
  defaultValue?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  size?: 'sm' | 'md' | 'lg';
  orientation?: 'horizontal' | 'vertical';
  className?: string;
  contentClassName?: string;
}

export const SecondaryTabs = ({
  tabs,
  defaultValue,
  value,
  onValueChange,
  size = 'md',
  orientation = 'horizontal',
  className = '',
  contentClassName = ''
}: SecondaryTabsProps) => {
  const initialValue = value || defaultValue || tabs[0]?.value || '';

  return (
    <Tabs
      defaultValue={initialValue}
      value={value}
      onValueChange={onValueChange}
      orientation={orientation}
      className={className}
    >
      <TabsList variant="underline" size={size}>
        {tabs.map((tab) => (
          <TabsTrigger 
            key={tab.value} 
            value={tab.value} 
            disabled={tab.disabled}
            variant="underline"
          >
            {tab.label}
          </TabsTrigger>
        ))}
      </TabsList>
      
      {tabs.map((tab) => (
        <TabsContent 
          key={tab.value} 
          value={tab.value}
          className={contentClassName}
        >
          {tab.content}
        </TabsContent>
      ))}
    </Tabs>
  );
};

// Simple Tabs Component (Legacy/Flexible - for custom usage)
interface SimpleTabsProps {
  tabs: TabItem[];
  defaultValue?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  variant?: 'default' | 'pills' | 'underline';
  size?: 'sm' | 'md' | 'lg';
  orientation?: 'horizontal' | 'vertical';
  className?: string;
  contentClassName?: string;
}

export const SimpleTabs = ({
  tabs,
  defaultValue,
  value,
  onValueChange,
  variant = 'default',
  size = 'md',
  orientation = 'horizontal',
  className = '',
  contentClassName = ''
}: SimpleTabsProps) => {
  const initialValue = value || defaultValue || tabs[0]?.value || '';

  return (
    <Tabs
      defaultValue={initialValue}
      value={value}
      onValueChange={onValueChange}
      orientation={orientation}
      className={className}
    >
      <TabsList variant={variant} size={size}>
        {tabs.map((tab) => (
          <TabsTrigger 
            key={tab.value} 
            value={tab.value} 
            disabled={tab.disabled}
            variant={variant}
          >
            {tab.label}
          </TabsTrigger>
        ))}
      </TabsList>
      
      {tabs.map((tab) => (
        <TabsContent 
          key={tab.value} 
          value={tab.value}
          className={contentClassName}
        >
          {tab.content}
        </TabsContent>
      ))}
    </Tabs>
  );
};

// Export all components
export { 
  type TabItem, 
  type TabsProps, 
  type TabsListProps, 
  type TabsTriggerProps, 
  type TabsContentProps, 
  type PrimaryTabsProps,
  type SecondaryTabsProps,
  type SimpleTabsProps 
};