import { TableFeatures } from '../types/table.types'

export const getDefaultFeatures = (): TableFeatures => ({
  sorting: true,
  filtering: true,
  globalFilter: true,
  pagination: true,
  grouping: false,
  expanding: false,
  columnResizing: true,
  columnOrdering: true,
  columnPinning: true,
  rowPinning: false,
  rowSelection: false,
})

export const mergeFeatures = (features?: TableFeatures): TableFeatures => {
  const defaults = getDefaultFeatures()
  return { ...defaults, ...features }
}

export const formatCellValue = (value: any): string => {
  if (value === null || value === undefined) return ''
  if (typeof value === 'boolean') return value ? 'Yes' : 'No'
  if (typeof value === 'number') return value.toLocaleString()
  if (value instanceof Date) return value.toLocaleDateString()
  return String(value)
}

export const getRowLevel = (row: any): number => {
  let level = 0
  let parent = row.getParentRow?.()
  while (parent) {
    level++
    parent = parent.getParentRow?.()
  }
  return level
}

export const getStatusBadgeClasses = (status: string): string => {
  const baseClasses = 'px-2 py-1 rounded-full text-sm font-medium'
  
  switch (status?.toLowerCase()) {
    case 'completed':
    case 'done':
      return `${baseClasses} bg-green-100 text-green-800`
    case 'in_progress':
    case 'active':
    case 'in progress':
      return `${baseClasses} bg-blue-100 text-blue-800`
    case 'upcoming':
    case 'pending':
      return `${baseClasses} bg-gray-100 text-gray-800`
    default:
      return `${baseClasses} bg-gray-100 text-gray-700`
  }
}

export const cn = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(' ')
}