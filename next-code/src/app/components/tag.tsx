'use client';

import { XMarkIcon } from '@heroicons/react/24/outline';

interface TagProps {
    children: React.ReactNode;
    variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error';
    size?: 'sm' | 'md' | 'lg';
    removable?: boolean;
    onRemove?: () => void;
    disabled?: boolean;
    className?: string;
    onClick?: () => void;
    icon?: React.ReactNode;
    iconPosition?: 'left' | 'right';
}

export const Tag = ({
    children,
    variant = 'default',
    size = 'md',
    removable = false,
    onRemove,
    disabled = false,
    className = '',
    onClick,
    icon,
    iconPosition = 'left'
}: TagProps) => {
    const baseClasses = 'inline-flex items-center font-medium rounded-full transition-colors duration-200 border';
    
    const sizeClasses = {
        sm: 'px-2 py-1 text-xs',
        md: 'px-3 py-1.5 text-sm',
        lg: 'px-4 py-2 text-base'
    };

    const variantClasses = {
        default: 'bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100',
        primary: 'bg-teal-50 text-teal-700 border-teal-500 hover:bg-teal-100',
        secondary: 'bg-teal-50 text-teal-700 border-teal-500 hover:bg-teal-100',
        success: 'bg-teal-50 text-teal-700 border-teal-500 hover:bg-teal-100',
        warning: 'bg-yellow-50 text-yellow-700 border-yellow-500 hover:bg-yellow-100',
        error: 'bg-teal-50 text-teal-700 border-teal-500 hover:bg-teal-100'
    };

    const iconSizeClasses = {
        sm: 'w-3 h-3',
        md: 'w-4 h-4',
        lg: 'w-5 h-5'
    };

    const handleRemove = (e: React.MouseEvent) => {
        e.stopPropagation();
        if (!disabled && onRemove) {
            onRemove();
        }
    };

    const handleClick = () => {
        if (!disabled && onClick) {
            onClick();
        }
    };

    const iconSpacing = size === 'sm' ? 'mr-1' : size === 'md' ? 'mr-1.5' : 'mr-2';
    const iconSpacingRight = size === 'sm' ? 'ml-1' : size === 'md' ? 'ml-1.5' : 'ml-2';

    return (
        <span
            className={`
                ${baseClasses}
                ${sizeClasses[size]}
                ${variantClasses[variant]}
                ${disabled ? 'opacity-50 cursor-not-allowed' : onClick ? 'cursor-pointer' : ''}
                ${className}
            `}
            onClick={handleClick}
        >
            {icon && iconPosition === 'left' && (
                <span className={`${iconSizeClasses[size]} ${iconSpacing}`}>
                    {icon}
                </span>
            )}
            {children}
            {icon && iconPosition === 'right' && (
                <span className={`${iconSizeClasses[size]} ${iconSpacingRight}`}>
                    {icon}
                </span>
            )}
            {removable && (
                <button
                    type="button"
                    onClick={handleRemove}
                    disabled={disabled}
                    className={`
                        ml-1.5 rounded-full hover:bg-black/10 transition-colors duration-200
                        ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'}
                        ${size === 'sm' ? 'p-0.5' : size === 'md' ? 'p-1' : 'p-1.5'}
                    `}
                >
                    <XMarkIcon className={iconSizeClasses[size]} />
                </button>
            )}
        </span>
    );
};

export default Tag;