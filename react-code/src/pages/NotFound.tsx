import type React from "react"
import { Link } from "react-router-dom"

const NotFound: React.FC = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-[#F1F5F9]">
      <div className="text-center">
        <h1 className="text-6xl font-bold text-[#111827]">404</h1>
        <p className="mt-4 text-xl text-[#4b5563]">Page not found</p>
        <p className="mt-2 text-[#4b5563]">The page you are looking for doesn't exist or has been moved.</p>
        <Link to="/" className="inline-block px-6 py-3 mt-6 text-white bg-[#00B2A1] rounded-md hover:bg-[#00A090]">
          Go to Dashboard
        </Link>
      </div>
    </div>
  )
}

export default NotFound
