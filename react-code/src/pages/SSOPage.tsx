"use client"

import type React from "react"
import { useEffect, useState } from "react"
import axios from "axios"

const SSOPage: React.FC = () => {
  const [platformDomain, setPlatformDomain] = useState<string>("")
  const [loading, setLoading] = useState<boolean>(true)

  // Fetch the PLATFORM_DOMAIN from the server on component mount
  useEffect(() => {
    const fetchPlatformDomain = async () => {
      try {
        setLoading(true)

        // Try to fetch the PLATFORM_DOMAIN from the server's config endpoint
        const response = await axios.get("/api/config")
        const domain = response.data.PLATFORM_DOMAIN

        if (domain) {
          console.log("Using PLATFORM_DOMAIN from server:", domain)
          setPlatformDomain(domain)
        } else {
          console.error("PLATFORM_DOMAIN not found in server config")
        }
      } catch (err) {
        console.error("Error fetching PLATFORM_DOMAIN:", err)
      } finally {
        setLoading(false)
      }
    }

    fetchPlatformDomain()
  }, [])

  const handleLogin = () => {
    if (!platformDomain) {
      alert("Cannot redirect: Sutra Platform domain is not configured.")
      return
    }

    console.log("Redirecting to Sutra Platform:", platformDomain)

    // Redirect the parent/top window to the Sutra platform
    if (window.top) {
      window.top.location.href = platformDomain
    } else {
      window.location.href = platformDomain
    }
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-[#F1F5F9]">
      <div className="w-full max-w-lg p-8 space-y-6 bg-white rounded-xl shadow-md border border-gray-200">
        <div className="text-center">
          <div className="flex justify-center mb-6">
            {/* Logo from environment variable */}
            <div className="w-16 h-16 flex items-center justify-center bg-[#3B4154] rounded-full overflow-hidden">
              {platformDomain ? (
                <img
                  src={process.env.REACT_APP_PLATFORM_LOGO || "/abstract-geometric-shapes.png"}
                  alt="Sutra Platform Logo"
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    // Fallback if image fails to load
                    const target = e.target as HTMLImageElement
                    target.onerror = null
                    target.src = "/abstract-geometric-shapes.png"
                    target.className = "w-10 h-10"
                    ;(target.parentNode as HTMLElement).classList.add("flex", "items-center", "justify-center")
                  }}
                />
              ) : (
                <span className="text-2xl font-bold text-white">S</span>
              )}
            </div>
          </div>
          <h1 className="text-3xl font-extrabold text-[#111827]">Authentication Required</h1>
          <p className="mt-2 text-[#4b5563]">Please authenticate through the Sutra Platform to continue</p>
        </div>

        <div className="border-t border-b border-gray-200 py-6">
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-[#111827]">Authentication Process:</h2>
            <ol className="space-y-3 list-decimal list-inside text-[#4b5563]">
              <li className="pl-2">Click the "Open Sutra Platform Login" button below</li>
              <li className="pl-2">Complete authentication on the Sutra Platform</li>
              <li className="pl-2">Return to this application after successful login</li>
              <li className="pl-2">Your session will be automatically established</li>
            </ol>
          </div>

          <div className="mt-6 p-4 bg-[#F1F5F9] rounded-md border border-gray-200">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg
                  className="h-5 w-5 text-[#3b82f6]"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-[#111827]">Important Note</h3>
                <p className="mt-1 text-sm text-[#4b5563]">
                  Clicking the button below will redirect you to the Sutra Platform login page. After successful
                  authentication, you'll need to return to this application. The authentication token will be
                  automatically passed to this app.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col space-y-4">
          {loading ? (
            <button disabled className="w-full px-4 py-3 text-white bg-gray-400 rounded-md cursor-not-allowed">
              <span className="inline-block w-4 h-4 mr-2 border-t-2 border-b-2 border-white rounded-full animate-spin"></span>
              Loading...
            </button>
          ) : (
            <button
              onClick={handleLogin}
              disabled={!platformDomain}
              className={`w-full px-4 py-3 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-[#00B2A1] focus:ring-offset-2 font-medium transition-colors duration-200 ${
                platformDomain ? "bg-[#00B2A1] hover:bg-[#00A090]" : "bg-gray-400 cursor-not-allowed"
              }`}
            >
              {platformDomain ? "Open Sutra Platform Login" : "Platform URL Not Configured"}
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

export default SSOPage
