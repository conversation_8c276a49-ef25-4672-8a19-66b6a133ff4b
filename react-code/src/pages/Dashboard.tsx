"use client"

import type React from "react"
import { useEffect, useState, useRef } from "react"
import { useSelector } from "react-redux"
import type { RootState } from "../redux/store"
import { fetchUserDepartments } from "../services/api"
import { getUserData } from "../utils/tokenStorage"

interface Department {
  id: number
  name: string
  department_role: "admin" | "editor" | "member"
}

const Dashboard: React.FC = () => {
  const { user } = useSelector((state: RootState) => state.auth)
  const [departments, setDepartments] = useState<Department[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const userData = getUserData() // Get user data from token
  const guideRef = useRef<HTMLDivElement>(null)
  const printFrameRef = useRef<HTMLIFrameElement | null>(null)

  useEffect(() => {
    const loadDepartments = async () => {
      try {
        const data = await fetchUserDepartments()
        setDepartments(data)
        setError(null)
      } catch (error) {
        console.error("Failed to fetch departments:", error)
        setError("Failed to load departments. Please try again later.")
      } finally {
        setLoading(false)
      }
    }

    loadDepartments()
  }, [])

  const handleDownloadPDF = () => {
    if (!guideRef.current) return

    // Create a new iframe for printing
    if (!printFrameRef.current) {
      const iframe = document.createElement("iframe")
      iframe.style.position = "fixed"
      iframe.style.right = "0"
      iframe.style.bottom = "0"
      iframe.style.width = "0"
      iframe.style.height = "0"
      iframe.style.border = "0"
      document.body.appendChild(iframe)
      printFrameRef.current = iframe
    }

    // Get the iframe document
    const iframeDoc = printFrameRef.current.contentDocument || printFrameRef.current.contentWindow?.document
    if (!iframeDoc) return

    // Get current user information
    const currentUser = user || userData || {}
    const userName = currentUser.name || "User"
    const userEmail = currentUser.email || "Not available"
    const userRole = currentUser.is_sys_admin || currentUser.isSysAdmin ? "System Admin" : "Regular User"
    const userId = currentUser.id || "Not available"
    const userCreatedAt = currentUser.created_at
      ? new Date(currentUser.created_at).toLocaleDateString()
      : "Not available"

    // Create a new document with just the guide content and print-friendly styles
    const htmlContent = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Sutra Application Boilerplate Guide</title>
        <style>
          @media print {
            @page {
              size: A4;
              margin: 1cm;
            }
            body {
              font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
              line-height: 1.5;
              color: #000;
              font-size: 12pt;
            }
            pre {
              background-color: #f8f8f8;
              border: 1px solid #ddd;
              padding: 10px;
              border-radius: 4px;
              overflow-x: auto;
              white-space: pre-wrap;
              font-size: 10pt;
            }
            code {
              font-family: monospace;
              background-color: #f8f8f8;
              padding: 2px 4px;
              border-radius: 2px;
              font-size: 10pt;
            }
            h1, h2, h3, h4 {
              page-break-after: avoid;
              page-break-inside: avoid;
            }
            h1 { font-size: 20pt; }
            h2 { font-size: 16pt; }
            h3 { font-size: 14pt; }
            h4 { font-size: 12pt; }
            p, li {
              page-break-inside: avoid;
            }
            table {
              page-break-inside: auto;
              border-collapse: collapse;
              width: 100%;
            }
            tr {
              page-break-inside: avoid;
              page-break-after: auto;
            }
            td, th {
              border: 1px solid #ddd;
              padding: 8px;
            }
            .info-box {
              border: 1px solid #4ade80;
              border-radius: 4px;
              padding: 10px;
              margin: 20px 0;
              background-color: #f0fdf4;
            }
            .card {
              border: 1px solid #ddd;
              border-radius: 4px;
              padding: 10px;
              margin-bottom: 16px;
              background-color: #f8f8f8;
            }
            a {
              text-decoration: underline;
              color: #000;
            }
            .page-break {
              page-break-before: always;
            }
            svg {
              display: none;
            }
            .grid {
              display: block;
            }
            .grid > div {
              margin-bottom: 20px;
            }
            .user-info {
              margin-top: 20px;
              padding: 15px;
              border: 1px solid #ddd;
              border-radius: 4px;
              background-color: #f8f8f8;
              page-break-inside: avoid;
            }
            .user-info h3 {
              margin-top: 0;
              font-size: 14pt;
            }
            .user-info table {
              width: 100%;
              margin-top: 10px;
            }
            .user-info th {
              text-align: left;
              width: 30%;
              padding: 5px;
            }
            .user-info td {
              padding: 5px;
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
            }
            .header h1 {
              margin-bottom: 5px;
            }
            .header p {
              margin-top: 0;
              font-style: italic;
              color: #555;
            }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Sutra Application Boilerplate Guide</h1>
          <p>Generated for: ${userName} (${userEmail})</p>
        </div>
        
        <div class="user-info">
          <h3>User Information</h3>
          <table>
            <tr>
              <th>Name:</th>
              <td>${userName}</td>
            </tr>
            <tr>
              <th>Email:</th>
              <td>${userEmail}</td>
            </tr>
            <tr>
              <th>User ID:</th>
              <td>${userId}</td>
            </tr>
            <tr>
              <th>Role:</th>
              <td>${userRole}</td>
            </tr>
            <tr>
              <th>Account Created:</th>
              <td>${userCreatedAt}</td>
            </tr>
          </table>
        </div>
        
        <div class="page-break"></div>
        
        ${guideRef.current.innerHTML}
      </body>
      </html>
    `

    // Write the content to the iframe
    iframeDoc.open()
    iframeDoc.write(htmlContent)
    iframeDoc.close()

    // Wait for content to load then print
    setTimeout(() => {
      if (printFrameRef.current?.contentWindow) {
        printFrameRef.current.contentWindow.focus()
        printFrameRef.current.contentWindow.print()
      }
    }, 500)
  }

  // Clean up the iframe when component unmounts
  useEffect(() => {
    return () => {
      if (printFrameRef.current && printFrameRef.current.parentNode) {
        printFrameRef.current.parentNode.removeChild(printFrameRef.current)
      }
    }
  }, [])

  if (loading) {
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="p-4 text-[#ef4444] bg-red-100 rounded-md">
          <p>{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 mt-4 text-white bg-[#ef4444] rounded hover:bg-red-600"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  // Ensure we have user data
  if (!user && !userData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="p-4 text-[#ef4444] bg-red-100 rounded-md">
          <p>User information not available. Please log in again.</p>
          <button
            onClick={() => (window.location.href = "/sso")}
            className="px-4 py-2 mt-4 text-white bg-[#ef4444] rounded hover:bg-red-600"
          >
            Go to Login
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Header with welcome message and download button */}
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold text-[#111827]">Welcome, {user?.name || userData?.name || "User"}</h1>
          <p className="text-[#4b5563]">
            @{user?.username || userData?.username || userData?.email?.split("@")[0] || "username"}
          </p>
          <p className="text-sm text-[#4b5563] mt-1">
            <span className="font-medium">Note:</span> User details are obtained from JWT authentication passed from the
            Sutra Platform
          </p>
        </div>

        <button
          onClick={handleDownloadPDF}
          className="px-4 py-2 text-white bg-[#00B2A1] rounded-md hover:bg-[#00A090] transition-colors flex items-center mt-4 md:mt-0"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
            />
          </svg>
          Download PDF
        </button>
      </div>

      {/* User Profile and Departments side by side */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* User Profile Card */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-[#111827] mb-4">User Profile</h2>
          <div className="flex items-center space-x-4 mb-4">
            {user?.thumbnail || userData?.thumbnail ? (
              <div className="w-16 h-16 overflow-hidden rounded-full border-2 border-[#00B2A1]/20">
                <img
                  src={user?.thumbnail || userData?.thumbnail}
                  alt={user?.name || userData?.name || "User"}
                  className="object-cover w-full h-full"
                  onError={(e) => {
                    // Handle image load error
                    const target = e.target as HTMLImageElement
                    target.onerror = null
                    target.src = "/abstract-geometric-shapes.png"
                  }}
                />
              </div>
            ) : (
              <div className="flex items-center justify-center w-16 h-16 text-xl font-medium text-white bg-[#3B4154] rounded-full border-2 border-[#00B2A1]/20">
                {(user?.name || userData?.name || "U").charAt(0)}
              </div>
            )}
            <div>
              <h3 className="text-lg font-medium text-[#111827]">{user?.name || userData?.name || "User"}</h3>
              <p className="text-[#4b5563]">{user?.email || userData?.email || "No email available"}</p>
              {user?.username && <p className="text-[#4b5563] text-sm">@{user.username}</p>}
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-[#4b5563]">User ID:</span>
              <span className="text-sm font-medium text-[#111827]">{user?.id || userData?.id || "N/A"}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-[#4b5563]">Role:</span>
              <span className="text-sm font-medium text-[#111827]">
                {user?.is_sys_admin || userData?.isSysAdmin ? (
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    System Admin
                  </span>
                ) : (
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Regular User
                  </span>
                )}
              </span>
            </div>
            {user?.created_at && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-[#4b5563]">Created:</span>
                <span className="text-sm font-medium text-[#111827]">
                  {new Date(user.created_at).toLocaleDateString()}
                </span>
              </div>
            )}
            {user?.api_key && (
              <div className="mt-4">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm text-[#4b5563]">API Key:</span>
                  <button
                    className="text-xs text-[#00B2A1] hover:text-[#00A090]"
                    onClick={() => {
                      if (user?.api_key) {
                        navigator.clipboard.writeText(user.api_key)
                        alert("API Key copied to clipboard!")
                      }
                    }}
                  >
                    Copy
                  </button>
                </div>
                <div className="bg-gray-100 p-2 rounded-md">
                  <p className="font-mono text-xs text-[#111827] truncate">{user.api_key}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Departments Card */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-[#111827] mb-4">Your Departments</h2>
          {departments.length > 0 ? (
            <div className="overflow-hidden rounded-lg border border-gray-200">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Department
                    </th>
                    <th
                      scope="col"
                      className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Role
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {departments.map((dept) => (
                    <tr key={dept.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{dept.name}</div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                          ${
                            dept.department_role === "admin"
                              ? "bg-purple-100 text-purple-800"
                              : dept.department_role === "editor"
                                ? "bg-blue-100 text-blue-800"
                                : "bg-green-100 text-green-800"
                          }`}
                        >
                          {dept.department_role}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : userData?.department && Array.isArray(userData.department) && userData.department.length > 0 ? (
            <div className="overflow-hidden rounded-lg border border-gray-200">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Department ID
                    </th>
                    <th
                      scope="col"
                      className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Role
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {userData.department.map((dept: any, index: number) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{dept.departmentId || "Unknown"}</div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                          {dept.departmentRole || "Unknown"}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8 bg-gray-50 rounded-lg border border-dashed border-gray-300">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-10 w-10 mx-auto text-gray-400 mb-3"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1}
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                />
              </svg>
              <p className="text-[#4b5563] text-sm">You don't belong to any departments yet.</p>
            </div>
          )}
        </div>
      </div>

      {/* Main Guide Content */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h2 className="text-2xl font-bold text-[#111827] mb-6">Sutra Application Boilerplate Guide</h2>

        <div className="prose max-w-none" ref={guideRef}>
          <div className="bg-[#f8fafc] border-l-4 border-[#00B2A1] p-4 mb-8">
            <p className="text-[#334155] m-0">
              <strong>Version 1.0.0</strong> - This boilerplate provides a robust foundation for building
              enterprise-grade applications with React, TypeScript, and Node.js. Use this guide to understand how to
              effectively utilize and configure this application.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <div className="bg-[#f1f5f9] rounded-lg p-6">
              <h3 className="text-lg font-semibold text-[#111827] mb-4 flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 mr-2 text-[#00B2A1]"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
                  />
                </svg>
                Tech Stack
              </h3>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <span className="w-24 text-[#4b5563] font-medium">Frontend:</span>
                  <span>React, TypeScript, Redux, TailwindCSS</span>
                </li>
                <li className="flex items-center">
                  <span className="w-24 text-[#4b5563] font-medium">Backend:</span>
                  <span>Node.js, Express, TypeScript</span>
                </li>
                <li className="flex items-center">
                  <span className="w-24 text-[#4b5563] font-medium">Database:</span>
                  <span>PostgreSQL</span>
                </li>
                <li className="flex items-center">
                  <span className="w-24 text-[#4b5563] font-medium">Auth:</span>
                  <span>JWT, SSO Integration</span>
                </li>
                <li className="flex items-center">
                  <span className="w-24 text-[#4b5563] font-medium">DevOps:</span>
                  <span>Docker, Docker Compose</span>
                </li>
              </ul>
            </div>

            <div className="bg-[#f1f5f9] rounded-lg p-6">
              <h3 className="text-lg font-semibold text-[#111827] mb-4 flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 mr-2 text-[#00B2A1]"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                  />
                </svg>
                Key Features
              </h3>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 mr-2 text-[#00B2A1] mt-0.5 shrink-0"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>SSO Authentication with JWT token validation</span>
                </li>
                <li className="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 mr-2 text-[#00B2A1] mt-0.5 shrink-0"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Redux with Redux Toolkit for state management</span>
                </li>
                <li className="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 mr-2 text-[#00B2A1] mt-0.5 shrink-0"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Axios for API requests with interceptors</span>
                </li>
                <li className="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 mr-2 text-[#00B2A1] mt-0.5 shrink-0"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Global error boundary and API error handling</span>
                </li>
                <li className="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 mr-2 text-[#00B2A1] mt-0.5 shrink-0"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Responsive design with TailwindCSS</span>
                </li>
              </ul>
            </div>
          </div>

          <h3 className="text-xl font-semibold text-[#111827] mb-4">Configuration Options</h3>
          <p>
            The application can be configured through environment variables. Create a <code>.env</code> file in the root
            directory with the following variables:
          </p>
          <div className="bg-gray-100 p-4 rounded-md font-mono text-sm my-4 overflow-x-auto">
            <pre>
              {`# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=sutra_db
DB_USER=postgres
DB_PASSWORD=postgres

# Authentication
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRATION=24h

# SSO Configuration
PLATFORM_DOMAIN=https://www.sutra.ai

# Logging
ELASTIC_INDEX=sutra-logs
LOG_LEVEL=info

# Client Configuration
REACT_APP_API_URL=http://localhost:5000
`}
            </pre>
          </div>

          <h3 className="text-xl font-semibold text-[#111827] mb-4">Authentication Flow</h3>
          <div className="bg-[#f1f5f9] rounded-lg p-6 mb-6">
            <ol className="list-decimal pl-6 space-y-2">
              <li>User accesses the application and is redirected to the SSO page if not authenticated</li>
              <li>User clicks "Log in with Sutra Platform" and is redirected to the Sutra Platform login page</li>
              <li>After successful authentication, the user is redirected back with a JWT token</li>
              <li>The token is validated and stored in local storage</li>
              <li>The token is included in all subsequent API requests</li>
              <li>User data is extracted from the JWT payload and used throughout the application</li>
            </ol>
          </div>

          <h3 className="text-xl font-semibold text-[#111827] mb-4">Getting User Details</h3>
          <div className="bg-[#f1f5f9] rounded-lg p-6 mb-6">
            <h4 className="font-medium text-[#111827] mb-3">Frontend (Client-side)</h4>
            <p className="mb-3">User details are available in two ways on the frontend:</p>
            <ol className="list-decimal pl-6 space-y-2 mb-4">
              <li>
                <strong>Redux Store:</strong> Access user data from the Redux store using the useSelector hook:
                <div className="bg-gray-100 p-3 rounded-md font-mono text-sm my-2 overflow-x-auto">
                  <pre>{`import { useSelector } from 'react-redux';
import type { RootState } from '../redux/store';

const MyComponent = () => {
  const { user } = useSelector((state: RootState) => state.auth);
  
  // Now you can access user properties
  const userName = user?.name;
  const userEmail = user?.email;
  const isAdmin = user?.is_sys_admin;
  
  return <div>Hello, {userName}</div>;
};`}</pre>
                </div>
              </li>
              <li>
                <strong>Local Storage:</strong> Get user data directly from the token in local storage:
                <div className="bg-gray-100 p-3 rounded-md font-mono text-sm my-2 overflow-x-auto">
                  <pre>{`import { getUserData } from '../utils/tokenStorage';

const MyComponent = () => {
  const userData = getUserData();
  
  // Now you can access user properties
  const userName = userData?.name;
  const userEmail = userData?.email;
  const isAdmin = userData?.isSysAdmin;
  
  return <div>Hello, {userName}</div>;
};`}</pre>
                </div>
              </li>
            </ol>

            <h4 className="font-medium text-[#111827] mb-3">Backend (Server-side)</h4>
            <p className="mb-3">
              On the backend, user details are available in the request object after JWT authentication:
            </p>
            <div className="bg-gray-100 p-3 rounded-md font-mono text-sm my-2 overflow-x-auto">
              <pre>{`// In your Express route handler
app.get('/api/some-endpoint', (req, res) => {
  // User data is available in req.user after JWT middleware
  const userId = req.user.id;
  const userName = req.user.name;
  const userEmail = req.user.email;
  const isAdmin = req.user.isSysAdmin;
  
  // Use the user data for authorization or personalization
  if (isAdmin) {
    // Allow admin operations
  } else {
    // Regular user operations
  }
  
  res.json({ message: \`Hello, \${userName}\` });
});`}</pre>
            </div>
            <p className="mt-3 text-[#4b5563]">
              <strong>Note:</strong> The JWT token is automatically extracted and verified by the{" "}
              <code>jwtExtractor</code> middleware, which populates <code>req.user</code> with the decoded token
              payload.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <div>
              <h3 className="text-xl font-semibold text-[#111827] mb-4">Development Workflow</h3>
              <div className="bg-[#f1f5f9] rounded-lg p-6">
                <ol className="list-decimal pl-6 space-y-2">
                  <li>Clone the repository</li>
                  <li>
                    Create a <code>.env</code> file with the required environment variables
                  </li>
                  <li>
                    Run <code>docker-compose up</code> to start the development environment
                  </li>
                  <li>
                    Access the application at <code>http://localhost:3000</code>
                  </li>
                </ol>
              </div>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-[#111827] mb-4">Deployment</h3>
              <div className="bg-[#f1f5f9] rounded-lg p-6">
                <ol className="list-decimal pl-6 space-y-2">
                  <li>
                    Build the Docker images: <code>docker-compose build</code>
                  </li>
                  <li>Deploy the containers to your hosting environment</li>
                  <li>Configure environment variables for production</li>
                  <li>Set up a reverse proxy (e.g., Nginx) for SSL termination</li>
                </ol>
              </div>
            </div>
          </div>

          <h3 className="text-xl font-semibold text-[#111827] mb-4">Adding New Features</h3>
          <div className="bg-[#f1f5f9] rounded-lg p-6 mb-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-[#111827] mb-2">Frontend</h4>
                <ul className="list-disc pl-6 space-y-1">
                  <li>
                    Create new React components in <code>src/components</code>
                  </li>
                  <li>
                    Add new pages in <code>src/pages</code>
                  </li>
                  <li>
                    Update routes in <code>App.tsx</code>
                  </li>
                  <li>
                    Add new Redux slices in <code>src/redux</code>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-[#111827] mb-2">Backend</h4>
                <ul className="list-disc pl-6 space-y-1">
                  <li>
                    Add new API endpoints in <code>server/routes</code>
                  </li>
                  <li>
                    Create controllers in <code>server/controllers</code>
                  </li>
                  <li>
                    Add services in <code>server/services</code>
                  </li>
                  <li>
                    Create middleware in <code>server/middlewares</code>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <h3 className="text-xl font-semibold text-[#111827] mb-4">API Integration</h3>
          <p>
            The application uses Axios for API requests. The API client is configured in{" "}
            <code>src/services/api.ts</code> and includes automatic token inclusion in request headers, response
            interceptors for error handling, and request timeout configuration.
          </p>
          <div className="bg-gray-100 p-4 rounded-md font-mono text-sm my-4 overflow-x-auto">
            <pre>
              {`// Example API service
import { api } from './api';

export const fetchUserData = async () => {
  const response = await api.get('/api/users/me');
  return response.data;
};

export const updateUserProfile = async (data) => {
  const response = await api.put('/api/users/profile', data);
  return response.data;
};`}
            </pre>
          </div>

          <h3 className="text-xl font-semibold text-[#111827] mb-4">JWT Authentication Details</h3>
          <div className="bg-[#f1f5f9] rounded-lg p-6 mb-6">
            <p className="mb-4">
              This application uses JWT (JSON Web Token) authentication passed from the Sutra Platform. The JWT token
              contains:
            </p>
            <ul className="list-disc pl-6 space-y-2">
              <li>
                <strong>User Identity:</strong> User ID, name, email, and other profile information
              </li>
              <li>
                <strong>Authorization Data:</strong> User roles, permissions, and department access
              </li>
              <li>
                <strong>Token Metadata:</strong> Expiration time, issuer, and other security information
              </li>
            </ul>
            <p className="mt-4">
              The token is validated on both the client and server sides to ensure security. User data displayed in this
              dashboard is extracted directly from the JWT payload.
            </p>
          </div>

          <h3 className="text-xl font-semibold text-[#111827] mb-4">Troubleshooting</h3>
          <div className="bg-[#f1f5f9] rounded-lg p-6 mb-6">
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-[#111827] mb-1">Authentication Issues</h4>
                <p className="text-[#4b5563]">
                  Check the JWT token validity and PLATFORM_DOMAIN configuration. Ensure the token is being properly
                  stored in local storage and included in API requests.
                </p>
              </div>
              <div>
                <h4 className="font-medium text-[#111827] mb-1">API Connection Errors</h4>
                <p className="text-[#4b5563]">
                  Verify the REACT_APP_API_URL is correctly set and the API server is running. Check network requests in
                  the browser developer tools for more details.
                </p>
              </div>
              <div>
                <h4 className="font-medium text-[#111827] mb-1">Database Errors</h4>
                <p className="text-[#4b5563]">
                  Ensure database credentials are correct and the database is accessible. Check the server logs for
                  connection errors.
                </p>
              </div>
              <div>
                <h4 className="font-medium text-[#111827] mb-1">Build Errors</h4>
                <p className="text-[#4b5563]">
                  Check for TypeScript errors and missing dependencies. Run <code>npm run build</code> to see detailed
                  error messages.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
