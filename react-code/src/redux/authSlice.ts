import { createSlice, createAsyncThunk, type PayloadAction } from "@reduxjs/toolkit"
import { verifyToken, fetchUserProfile } from "../services/api"
import { clearToken, getUserData } from "../utils/tokenStorage"

interface User {
  id: string
  name: string
  email: string
  username?: string
  thumbnail?: string
  about?: string
  is_sys_admin: boolean
  api_key?: string
  created_at: string
  updated_at: string
}

interface AuthState {
  isAuthenticated: boolean
  user: User | null
  loading: boolean
  error: string | null
  lastVerified: number | null
}

// Initialize state from local storage if available
const userData = getUserData()
const initialState: AuthState = {
  isAuthenticated: !!userData,
  user: userData
    ? {
        id: userData.id,
        name: userData.name,
        email: userData.email,
        username: userData.username,
        thumbnail: userData.thumbnail,
        about: userData.about,
        is_sys_admin: userData.isSysAdmin || false,
        api_key: userData.api_key,
        created_at: userData.created_at || new Date().toISOString(),
        updated_at: userData.updated_at || new Date().toISOString(),
      }
    : null,
  loading: false,
  error: null,
  lastVerified: userData ? Date.now() : null,
}

// Verify authentication token
export const verifyAuth = createAsyncThunk("auth/verifyAuth", async (token: string, { rejectWithValue }) => {
  try {
    const isValid = await verifyToken(token)

    if (isValid) {
      try {
        const user = await fetchUserProfile()
        return user
      } catch (error) {
        return rejectWithValue("Failed to fetch user profile")
      }
    }

    clearToken()
    return rejectWithValue("Invalid token")
  } catch (error) {
    clearToken()
    return rejectWithValue(error instanceof Error ? error.message : "Authentication failed")
  }
})

// Initialize auth from stored token
export const initializeAuth = createAsyncThunk("auth/initializeAuth", async (_, { dispatch, rejectWithValue }) => {
  try {
    const userData = getUserData()

    if (!userData || !userData.id) {
      return rejectWithValue("No valid user data found")
    }

    // Fetch fresh user data from API
    const user = await fetchUserProfile()
    return user
  } catch (error) {
    console.error("Failed to initialize auth:", error)
    return rejectWithValue(error instanceof Error ? error.message : "Failed to initialize authentication")
  }
})

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<User>) => {
      state.isAuthenticated = true
      state.user = action.payload
      state.loading = false
      state.error = null
      state.lastVerified = Date.now()
    },
    logout: (state) => {
      clearToken()
      state.isAuthenticated = false
      state.user = null
      state.error = null
      state.lastVerified = null
    },
    clearError: (state) => {
      state.error = null
    },
    refreshUserData: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload }
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Handle verifyAuth
      .addCase(verifyAuth.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(verifyAuth.fulfilled, (state, action) => {
        state.isAuthenticated = true
        state.user = action.payload
        state.loading = false
        state.error = null
        state.lastVerified = Date.now()
      })
      .addCase(verifyAuth.rejected, (state, action) => {
        state.isAuthenticated = false
        state.user = null
        state.loading = false
        state.error = (action.payload as string) || "Authentication failed"
        state.lastVerified = null
        clearToken()
      })

      // Handle initializeAuth
      .addCase(initializeAuth.pending, (state) => {
        state.loading = true
      })
      .addCase(initializeAuth.fulfilled, (state, action) => {
        state.isAuthenticated = true
        state.user = action.payload
        state.loading = false
        state.error = null
        state.lastVerified = Date.now()
      })
      .addCase(initializeAuth.rejected, (state, action) => {
        // Don't clear auth state on initialization failure
        // Just mark as not loading and set the error
        state.loading = false
        state.error = (action.payload as string) || "Failed to initialize authentication"
      })
  },
})

export const { setUser, logout, clearError, refreshUserData } = authSlice.actions
export default authSlice.reducer
