import axios from "axios"
import { getToken, clearToken, setToken } from "../utils/tokenStorage"

// Get API URL from environment or use default
const API_URL = process.env.REACT_APP_API_URL || "http://localhost:5000"

// Create axios instance with default config
const api = axios.create({
  baseURL: API_URL,
  timeout: 15000, // 15 seconds timeout
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
})

// Add request interceptor to include authToken
api.interceptors.request.use(
  (config) => {
    const token = getToken()

    if (token) {
      // Add authToken as query parameter for GET requests
      if (config.method?.toLowerCase() === "get") {
        config.params = {
          ...config.params,
          authToken: token,
        }
      }

      // Add Authorization header for all requests
      // @ts-ignore - Bypass TypeScript checking for this line
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

// Add response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle network errors
    if (!error.response) {
      console.error("Network error:", error.message)
      return Promise.reject(new Error("Network error. Please check your connection."))
    }

    // Handle authentication errors
    if (error.response.status === 401) {
      console.error("Authentication error:", error.response.data)
      clearToken()

      // Redirect to SSO page
      if (typeof window !== "undefined") {
        window.location.href = "/sso"
      }
    }

    // Handle server errors
    if (error.response.status >= 500) {
      console.error("Server error:", error.response.data)
      return Promise.reject(new Error("Server error. Please try again later."))
    }

    return Promise.reject(error)
  },
)

// Verify token with backend
export const verifyToken = async (token: string): Promise<boolean> => {
  try {
    const response = await axios.get(`${API_URL}/api/auth/verify`, {
      params: { authToken: token },
    })

    return response.data.valid
  } catch (error) {
    console.error("Token verification failed:", error)
    return false
  }
}

// Refresh token
export const refreshToken = async () => {
  try {
    const token = getToken()
    if (!token) {
      throw new Error("No token available to refresh")
    }

    const response = await axios.post(
      `${API_URL}/api/auth/refresh`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    )

    if (response.data.token) {
      setToken(response.data.token, 3600) // 1 hour
    }

    return response.data
  } catch (error) {
    console.error("Token refresh failed:", error)
    throw new Error("Failed to refresh authentication token")
  }
}

// Fetch user profile
export const fetchUserProfile = async () => {
  try {
    const response = await api.get("/api/users/me")
    return response.data
  } catch (error) {
    console.error("Failed to fetch user profile:", error)
    throw new Error("Failed to fetch user profile")
  }
}

// Fetch user departments
export const fetchUserDepartments = async () => {
  try {
    const response = await api.get("/api/users/me/departments")
    return response.data
  } catch (error) {
    console.error("Failed to fetch user departments:", error)
    throw new Error("Failed to fetch user departments")
  }
}

export default api
