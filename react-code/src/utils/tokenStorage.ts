// Simplified token storage utility

/**
 * Store token with expiration
 * @param token JWT token
 * @param expiresIn Expiration time in seconds (default: 3600)
 */
export const setToken = (token: string, expiresIn = 3600): void => {
  try {
    const expiresAt = new Date().getTime() + expiresIn * 1000

    localStorage.setItem("authToken", token)
    localStorage.setItem("tokenExpiry", expiresAt.toString())

    // Parse and store user data from token
    const userData = parseJwt(token)
    if (userData) {
      localStorage.setItem("userData", JSON.stringify(userData))
    }
  } catch (error) {
    console.error("Error storing token:", error)
  }
}

/**
 * Get token if it's still valid
 * @returns Valid token or null
 */
export const getToken = (): string | null => {
  try {
    const token = localStorage.getItem("authToken")
    const expiryStr = localStorage.getItem("tokenExpiry")

    if (!token || !expiryStr) {
      return null
    }

    const expiry = Number.parseInt(expiryStr, 10)
    const now = new Date().getTime()

    // If token is expired, clear it and return null
    if (now >= expiry) {
      clearToken()
      return null
    }

    return token
  } catch (error) {
    console.error("Error retrieving token:", error)
    return null
  }
}

/**
 * Clear token from storage
 */
export const clearToken = (): void => {
  try {
    localStorage.removeItem("authToken")
    localStorage.removeItem("tokenExpiry")
    localStorage.removeItem("userData")
  } catch (error) {
    console.error("Error clearing token:", error)
  }
}

/**
 * Check if user is authenticated
 * @returns boolean indicating authentication status
 */
export const isAuthenticated = (): boolean => {
  return getToken() !== null
}

/**
 * Parse JWT without verification (for client-side use only)
 * @param token JWT token
 * @returns Decoded payload or null
 */
export const parseJwt = (token: string): any => {
  try {
    if (!token) {
      return null
    }

    const parts = token.split(".")
    if (parts.length !== 3) {
      return null
    }

    const base64Url = parts[1]
    const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/")

    try {
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split("")
          .map((c) => "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2))
          .join(""),
      )

      return JSON.parse(jsonPayload)
    } catch (e) {
      console.error("Error parsing JWT payload:", e)
      return null
    }
  } catch (error) {
    console.error("Error parsing JWT:", error)
    return null
  }
}

/**
 * Get user data from stored token
 * @returns User data or null
 */
export const getUserData = (): any => {
  try {
    const userData = localStorage.getItem("userData")
    if (!userData) {
      return null
    }

    try {
      return JSON.parse(userData)
    } catch (e) {
      console.error("Error parsing user data:", e)
      localStorage.removeItem("userData")
      return null
    }
  } catch (error) {
    console.error("Error getting user data:", error)
    return null
  }
}

/**
 * Check if token needs refresh (less than 5 minutes remaining)
 * @returns boolean indicating if refresh is needed
 */
export const needsRefresh = (): boolean => {
  try {
    const expiryStr = localStorage.getItem("tokenExpiry")
    if (!expiryStr) return false

    const expiry = Number.parseInt(expiryStr, 10)
    const now = new Date().getTime()

    // If less than 5 minutes remaining, refresh is needed
    return expiry - now < 5 * 60 * 1000
  } catch (error) {
    console.error("Error checking token refresh:", error)
    return false
  }
}

/**
 * Validate token on frontend
 * @param token JWT token
 * @returns boolean indicating if token is valid
 */
export const validateToken = (token: string): boolean => {
  try {
    if (!token) {
      return false
    }

    // Check token format (should be 3 parts separated by dots)
    const parts = token.split(".")
    if (parts.length !== 3) {
      return false
    }

    const payload = parseJwt(token)

    // Check if token has required fields
    if (!payload || !payload.id || !payload.name || !payload.email) {
      return false
    }

    // Check if token is expired
    if (payload.exp && payload.exp * 1000 < Date.now()) {
      return false
    }

    return true
  } catch (error) {
    console.error("Token validation error:", error)
    return false
  }
}
