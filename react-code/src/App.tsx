"use client"

import type React from "react"
import { useEffect, useState } from "react"
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom"
import { useDispatch, useSelector } from "react-redux"
import { verifyAuth, initializeAuth } from "./redux/authSlice"
import type { RootState, AppDispatch } from "./redux/store"
import { getToken, setToken, clearToken } from "./utils/tokenStorage"

// Pages
import Dashboard from "./pages/Dashboard"
import SSOPage from "./pages/SSOPage"
import NotFound from "./pages/NotFound"

// Components
import Layout from "./components/Layout"
import ProtectedRoute from "./components/ProtectedRoute"
import ErrorBoundary from "./components/ErrorBoundary"

const App: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>()
  const { isAuthenticated, loading, error } = useSelector((state: RootState) => state.auth)
  const [isInitialized, setIsInitialized] = useState(false)

  useEffect(() => {
    const initAuth = async () => {
      try {
        // Get authToken from URL query params
        const urlParams = new URLSearchParams(window.location.search)
        const authToken = urlParams.get("authToken")

        if (authToken) {
          console.log("Found token in URL")

          // Store token in localStorage with 1 hour expiry
          setToken(authToken, 3600)

          // Clean URL by removing authToken
          const newUrl = window.location.pathname
          window.history.replaceState({}, document.title, newUrl)

          // Verify token with backend
          try {
            await dispatch(verifyAuth(authToken)).unwrap()
            console.log("Token verified successfully")
          } catch (error) {
            console.error("Token verification failed:", error)
            clearToken()
            // Redirect to SSO page instead of showing error
            window.location.href = "/sso"
            return
          }
        } else {
          // Check if token exists in localStorage
          const storedToken = getToken()
          if (storedToken) {
            console.log("Found stored token")

            try {
              // Initialize auth from stored token
              await dispatch(initializeAuth()).unwrap()
              console.log("Auth initialized from stored token")
            } catch (error) {
              console.error("Auth initialization failed:", error)
              clearToken()
              // Redirect to SSO page instead of showing error
              window.location.href = "/sso"
              return
            }
          } else {
            console.log("No token found, user needs to log in")
          }
        }
      } catch (error) {
        console.error("Authentication initialization error:", error)
        clearToken()
        // Redirect to SSO page instead of showing error
        window.location.href = "/sso"
        return
      } finally {
        setIsInitialized(true)
      }
    }

    initAuth()
  }, [dispatch])

  // Show loading indicator while initializing
  if (!isInitialized) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="w-12 h-12 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin"></div>
        <p className="mt-4 text-lg">Loading application...</p>
      </div>
    )
  }

  return (
    <ErrorBoundary>
      <Router>
        <Routes>
          <Route path="/sso" element={isAuthenticated ? <Navigate to="/" replace /> : <SSOPage />} />

          <Route element={<Layout />}>
            <Route
              path="/"
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated} loading={loading} error={error}>
                  <Dashboard />
                </ProtectedRoute>
              }
            />

            {/* Add more protected routes here */}
          </Route>

          <Route path="/404" element={<NotFound />} />
          <Route path="*" element={<Navigate to="/404" replace />} />
        </Routes>
      </Router>
    </ErrorBoundary>
  )
}

export default App
