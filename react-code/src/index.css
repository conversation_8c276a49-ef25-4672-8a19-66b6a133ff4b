@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Primary Colors */
  --sidebar-bg: #3b4154;
  --primary: #00b2a1;
  --white: #ffffff;
  --page-bg: #f1f5f9;

  /* Chart Colors */
  --chart-blue: #3b82f6;
  --chart-purple: #8b5cf6;
  --chart-green: #10b981;

  /* Text Colors */
  --sidebar-text: #cbd5e1;
  --secondary-text: #4b5563;
  --primary-text: #111827;

  /* Semantic Colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans",
    "Droid Sans", "Helvetica Neue", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--page-bg);
  color: var(--primary-text);
}

code {
  font-family: source-code-pro, <PERSON>lo, Monaco, Consolas, "Courier New", monospace;
}
