"use client"

import type React from "react"
import { useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { logout } from "../redux/authSlice"
import type { RootState, AppDispatch } from "../redux/store"

const Navbar: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>()
  const { user } = useSelector((state: RootState) => state.auth)
  const [dropdownOpen, setDropdownOpen] = useState(false)

  const handleLogout = () => {
    dispatch(logout())
  }

  const toggleDropdown = () => {
    setDropdownOpen(!dropdownOpen)
  }

  return (
    <nav className="flex items-center justify-between px-4 py-3 bg-white border-b border-gray-200">
      <div className="flex items-center">
        <span className="text-xl font-semibold text-[#111827]">Sutra Boilerplate</span>
      </div>

      <div className="flex items-center space-x-4">
        <div className="relative">
          <button
            className="flex items-center space-x-2 focus:outline-none"
            onClick={toggleDropdown}
            aria-haspopup="true"
            aria-expanded={dropdownOpen}
          >
            <div className="w-8 h-8 overflow-hidden bg-gray-300 rounded-full">
              {user?.thumbnail ? (
                <img
                  src={user.thumbnail || "/placeholder.svg"}
                  alt={user.name}
                  className="object-cover w-full h-full"
                />
              ) : (
                <div className="flex items-center justify-center w-full h-full text-sm font-medium text-[#4b5563]">
                  {user?.name?.charAt(0)}
                </div>
              )}
            </div>
            <span className="text-sm font-medium text-[#4b5563]">{user?.name}</span>
          </button>

          {dropdownOpen && (
            <div className="absolute right-0 w-48 py-2 mt-2 bg-white rounded-md shadow-lg">
              <button
                className="block w-full px-4 py-2 text-sm text-left text-[#4b5563] hover:bg-[#F1F5F9]"
                onClick={() => {
                  setDropdownOpen(false)
                  // Handle profile click
                }}
              >
                Profile
              </button>
              <button
                className="block w-full px-4 py-2 text-sm text-left text-[#4b5563] hover:bg-[#F1F5F9]"
                onClick={() => {
                  setDropdownOpen(false)
                  // Handle settings click
                }}
              >
                Settings
              </button>
              <button
                onClick={() => {
                  handleLogout()
                  setDropdownOpen(false)
                }}
                className="block w-full px-4 py-2 text-sm text-left text-[#4b5563] hover:bg-[#F1F5F9]"
              >
                Logout
              </button>
            </div>
          )}
        </div>
      </div>
    </nav>
  )
}

export default Navbar
