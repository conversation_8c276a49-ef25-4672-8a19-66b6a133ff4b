"use client"

import type React from "react"
import { Navigate } from "react-router-dom"

interface ProtectedRouteProps {
  isAuthenticated: boolean
  loading: boolean
  error?: string | null
  children: React.ReactNode
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ isAuthenticated, loading, error, children }) => {
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-[#F1F5F9]">
        <div className="w-12 h-12 border-t-2 border-b-2 border-[#00B2A1] rounded-full animate-spin"></div>
        <p className="mt-4 text-lg text-[#111827]">Loading...</p>
      </div>
    )
  }

  // If there's an error or not authenticated, redirect to SSO
  if (error || !isAuthenticated) {
    return <Navigate to="/sso" replace />
  }

  return <>{children}</>
}

export default ProtectedRoute
