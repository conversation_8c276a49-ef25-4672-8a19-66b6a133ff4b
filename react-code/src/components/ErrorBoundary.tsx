"use client"

import { Component, type ErrorInfo, type ReactNode } from "react"

interface Props {
  children: ReactNode
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    }
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error, errorInfo: null }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // You can also log the error to an error reporting service
    console.error("Error caught by ErrorBoundary:", error, errorInfo)
    this.setState({
      error,
      errorInfo,
    })
  }

  render(): ReactNode {
    if (this.state.hasError) {
      // You can render any custom fallback UI
      return (
        <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-[#F1F5F9]">
          <div className="w-full max-w-md p-6 bg-white rounded-xl shadow-md border border-gray-200">
            <h2 className="mb-4 text-2xl font-bold text-[#ef4444]">Something went wrong</h2>
            <p className="mb-4 text-[#4b5563]">
              The application encountered an unexpected error. Please try refreshing the page.
            </p>
            {this.state.error && (
              <div className="p-3 mb-4 overflow-auto text-sm bg-gray-100 rounded max-h-40">
                <p className="font-mono text-[#ef4444]">{this.state.error.toString()}</p>
              </div>
            )}
            <div className="flex space-x-4">
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 text-white bg-[#00B2A1] rounded hover:bg-[#00A090]"
              >
                Refresh Page
              </button>
              <button
                onClick={() => (window.location.href = "/sso")}
                className="px-4 py-2 text-white bg-[#4b5563] rounded hover:bg-gray-700"
              >
                Go to Login
              </button>
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary
