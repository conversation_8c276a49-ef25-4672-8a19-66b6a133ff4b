{"name": "sutra-boilerplate-client", "version": "1.0.0", "private": true, "dependencies": {"@reduxjs/toolkit": "^1.9.5", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.38", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "ajv": "^8.17.1", "axios": "^1.4.0", "cross-env": "^7.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.1.1", "react-router-dom": "^6.14.1", "react-scripts": "5.0.1", "tailwindcss": "^3.3.2", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "cross-env NODE_OPTIONS=--openssl-legacy-provider react-scripts start", "build": "cross-env NODE_OPTIONS=--openssl-legacy-provider react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "bundleDependencies": ["ajv"]}