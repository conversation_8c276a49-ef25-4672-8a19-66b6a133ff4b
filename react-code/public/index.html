<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Sutra Boilerplate Application"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <title>Sutra Boilerplate</title>
    <!-- Error handling for older browsers -->
    <script>
      window.onerror = function(message, source, lineno, colno, error) {
        // Log the error
        console.error('Global error:', message, source, lineno, colno, error);
        
        // Check if the error is related to unsupported browser features
        if (message && (
          message.includes('Promise') || 
          message.includes('fetch') || 
          message.includes('Symbol') ||
          message.includes('Map') ||
          message.includes('Set')
        )) {
          // Show a user-friendly message for outdated browsers
          document.body.innerHTML = '<div style="padding: 20px; text-align: center;"><h1>Browser Not Supported</h1><p>Please update your browser to a newer version to use this application.</p></div>';
        }
        
        return false;
      };
    </script>
  </head>
  <body>
    <noscript>
      <div style="padding: 20px; text-align: center;">
        <h1>JavaScript Required</h1>
        <p>You need to enable JavaScript to run this app.</p>
      </div>
    </noscript>
    <div id="root"></div>
    <!-- Fallback for loading errors -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          var rootElement = document.getElementById('root');
          if (rootElement && !rootElement.hasChildNodes()) {
            rootElement.innerHTML = '<div style="padding: 20px; text-align: center;"><h1>Loading Error</h1><p>The application failed to load. Please refresh the page or try again later.</p><button onclick="window.location.reload()" style="padding: 10px 20px; margin-top: 20px; background-color: #3b82f6; color: white; border: none; border-radius: 4px; cursor: pointer;">Refresh Page</button></div>';
          }
        }, 10000); // Check after 10 seconds
      });
    </script>
  </body>
</html>
