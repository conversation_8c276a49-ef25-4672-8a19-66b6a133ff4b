import httpService from './http';
import { UserProfile } from '@/types/auth';

export const userService = {
  // Get user profile
  async getUserProfile(): Promise<UserProfile> {
    return httpService.get<UserProfile>('/users/profile');
  },

  // Update user profile
  async updateUserProfile(profileData: Partial<UserProfile>): Promise<UserProfile> {
    return httpService.put<UserProfile>('/users/profile', profileData);
  },

  // Get user by ID
  async getUserById(userId: string): Promise<UserProfile> {
    return httpService.get<UserProfile>(`/users/${userId}`);
  },
};