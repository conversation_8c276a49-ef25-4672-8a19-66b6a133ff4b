/* eslint-disable @typescript-eslint/no-explicit-any */
import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  InternalAxiosRequestConfig,
} from 'axios';
import { API_URL } from '@/constants/api';

interface HttpServiceConfig {
  tokenProvider?: () => string | null;
  onAuthError?: () => void;
}

// --- Base HttpService Class --- 
class HttpService {
  protected readonly axiosInstance: AxiosInstance;
  private config: HttpServiceConfig = {};

  public constructor(baseURL: string) {
    this.axiosInstance = axios.create({
      baseURL,
    });

    this.initializeInterceptors();
  }

  public setup(config: HttpServiceConfig) {
    this.config = config;
  }

  private initializeInterceptors() {
    this.axiosInstance.interceptors.request.use(
      this.handleRequest,
      this.handleError
    );
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      this.handleError
    );
  }

  private handleRequest = (config: InternalAxiosRequestConfig) => {
    const token = this.config.tokenProvider?.();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  };

  private handleError = (error: any) => {
    // If 401 Unauthorized or token expired
    if (error.response?.status === 401 || 
        (error.response?.status === 403 && error.response?.data?.message === 'Token expired')) {
      this.config.onAuthError?.();
    }
    
    return Promise.reject(error);
    
    return Promise.reject(error);
  };

  public get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.axiosInstance.get<T>(url, config).then((res) => res.data);
  }

  public post<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    return this.axiosInstance.post<T>(url, data, config).then((res) => res.data);
  }

  public put<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    return this.axiosInstance.put<T>(url, data, config).then((res) => res.data);
  }

  public delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.axiosInstance.delete<T>(url, config).then((res) => res.data);
  }
}

// --- Singleton Instance --- 
const httpService = new HttpService(
  API_URL
);

export default httpService;