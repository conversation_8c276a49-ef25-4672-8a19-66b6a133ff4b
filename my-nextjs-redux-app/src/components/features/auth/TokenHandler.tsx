'use client';

import { useEffect, useRef } from 'react';
import { useAuth } from '@/hooks/useAuth';

const TokenHandler = () => {
  const { handleTokenFromUrl } = useAuth();
  const processed = useRef(false);

  useEffect(() => {
    if (processed.current) return;
    processed.current = true;

    handleTokenFromUrl();
  }, [handleTokenFromUrl]);

  return null; // This component does not render anything
};

export default TokenHandler;
