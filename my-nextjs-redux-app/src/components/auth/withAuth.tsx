'use client';

import React from 'react';
import { useSelector } from 'react-redux';
import { NotAuthenticated } from './NotAuthenticated';
import { RootState } from '@/store';

/**
 * Higher-order component to protect routes that require authentication
 * @param Component The component to render if authenticated
 * @param message Optional custom message to display when not authenticated
 */
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  message?: string
) {
  return function WithAuth(props: P) {
    // Get authentication state from Redux store
    const { isAuthenticated } = useSelector((state: RootState) => state.auth);

    // If not authenticated, show the not authenticated component
    if (!isAuthenticated) {
      return <NotAuthenticated message={message} />;
    }

    // If authenticated, render the protected component
    return <Component {...props} />;
  };
}