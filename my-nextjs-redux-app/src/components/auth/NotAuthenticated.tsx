'use client';

import React from 'react';

interface NotAuthenticatedProps {
  message?: string;
}

/**
 * Component to display when a user is not authenticated
 */
export function NotAuthenticated({ message = 'Not authenticated' }: NotAuthenticatedProps) {
  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50">
      <div className="rounded-lg bg-white p-8 shadow-md">
        <h1 className="mb-4 text-2xl font-bold text-red-600">Authentication Required</h1>
        <p className="text-gray-700">{message}</p>
        <p className="mt-4 text-gray-600">
          Please ensure you have a valid authentication token in the URL.
        </p>
      </div>
    </div>
  );
}