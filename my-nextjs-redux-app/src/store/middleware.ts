import { Middleware } from '@reduxjs/toolkit';

// Custom middleware for logging actions in development
const loggerMiddleware: Middleware = (store) => (next) => (action) => {
  if (process.env.NODE_ENV !== 'production' && typeof action === 'object' && action !== null && 'type' in action) {
    console.group(action.type as string);
    console.info('dispatching', action);
    const result = next(action);
    console.log('next state', store.getState());
    console.groupEnd();
    return result;
  }
  return next(action);
};

// Custom middleware for API error handling
const apiErrorMiddleware: Middleware = () => (next) => (action) => {
  // Check if the action is a rejected API call
  if (typeof action === 'object' && action !== null && 'type' in action && typeof action.type === 'string' && action.type.endsWith('/rejected')) {
    // Handle API errors (e.g., show toast notification)
    console.error('API Error:', 'error' in action ? action.error : 'Unknown error');
  }
  return next(action);
};

// Export all middleware as an array
export const middleware = [loggerMiddleware, apiErrorMiddleware];