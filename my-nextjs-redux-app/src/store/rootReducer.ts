import { combineReducers } from '@reduxjs/toolkit';
import { persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';

// Import slices
import { authReducer } from './slices/authSlice';
import { userReducer } from './slices/userSlice';
import { uiReducer } from './slices/uiSlice';

// Configure persistence for auth
const authPersistConfig = {
  key: 'auth',
  storage,
  whitelist: ['token', 'refreshToken', 'isAuthenticated'],
};

// Configure persistence for user
const userPersistConfig = {
  key: 'user',
  storage,
  whitelist: ['profile'],
};

// Combine all reducers
export const rootReducer = combineReducers({
  auth: persistReducer(authPersistConfig, authReducer),
  user: persistReducer(userPersistConfig, userReducer),
  ui: uiReducer,
});