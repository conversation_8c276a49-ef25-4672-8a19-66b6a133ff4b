import { jwtDecode } from 'jwt-decode';

interface DecodedToken {
  exp: number;
  sub: string;
  email: string;
  name: string;
  [key: string]: unknown;
}

interface UserProfile {
  id: string;
  email: string;
  name: string;
}

/**
 * Extracts 'authToken' from the current URL.
 * @returns The token string or null if not found.
 */
const extractTokenFromUrl = (): string | null => {
  if (typeof window === 'undefined') return null;
  
  const params = new URLSearchParams(window.location.search);
  return params.get('authToken');
};

/**
 * Checks if a JWT is expired.
 * @param token The JWT string.
 * @returns True if the token is expired, false otherwise.
 */
const isTokenExpired = (token: string): boolean => {
  try {
    const decoded: DecodedToken = jwtDecode(token);
    const currentTime = Date.now() / 1000;
    return decoded.exp < currentTime;
  } catch {
    return true;
  }
};

/**
 * Decodes a token and extracts user profile information.
 * @param token The JWT string.
 * @returns User profile information or null if invalid.
 */
const extractUserProfile = (token: string): UserProfile | null => {
  try {
    const decoded: DecodedToken = jwtDecode(token);
    return {
      id: decoded.sub,
      email: decoded.email,
      name: decoded.name,
    };
  } catch {
    return null;
  }
};

export const tokenUtils = {
  extractTokenFromUrl,
  isTokenExpired,
  extractUserProfile,
};