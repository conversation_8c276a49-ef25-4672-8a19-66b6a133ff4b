# Next.js Redux Authentication App

A modern web application built with Next.js, Redux Toolkit, TypeScript, and Tailwind CSS that implements a complete authentication flow with token-based authentication.

## Features

- **Next.js 15 with App Router**: Modern React framework with file-based routing
- **Redux Toolkit**: Efficient state management with Redux Toolkit
- **Token-based Authentication**: Secure authentication using JWT tokens
- **Persistent Authentication**: Token persistence using Redux Persist
- **Protected Routes**: Route protection using Next.js App Router conventions
- **TypeScript**: Full type safety throughout the application
- **Tailwind CSS**: Utility-first CSS framework for responsive design
- **API Integration**: Axios-based HTTP client with auth token handling
- **Error Handling**: Graceful error handling and user feedback

## Getting Started

### Prerequisites

- Node.js 18.0 or later
- npm or yarn

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd my-nextjs-redux-app

# Install dependencies
npm install

# Start the development server
npm run dev
```

### Environment Variables

Create a `.env.local` file in the root directory with the following variables:

```
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001/api
NEXT_PUBLIC_SSO_URL=https://sso.example.com
```

## Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── (protected)/        # Protected routes (require authentication)
│   │   ├── dashboard/      # Dashboard pages
│   │   └── layout.tsx      # Protected layout with auth check
│   ├── layout.tsx          # Root layout with providers
│   └── page.tsx            # Home page with auth redirect
├── components/             # React components
│   ├── features/           # Feature-specific components
│   │   ├── auth/           # Authentication components
│   │   └── layout/         # Layout components
├── hooks/                  # Custom React hooks
│   └── useAuth.ts          # Authentication hook
├── lib/                    # Library code
│   └── providers.tsx       # Provider components
├── services/               # API services
│   ├── authService.ts      # Authentication service
│   ├── http.ts             # HTTP client with interceptors
│   └── userService.ts      # User data service
├── store/                  # Redux store
│   ├── slices/             # Redux slices
│   │   ├── authSlice.ts    # Authentication state
│   │   └── userSlice.ts    # User state
│   ├── index.ts            # Store configuration
│   ├── middleware.ts       # Redux middleware
│   └── rootReducer.ts      # Root reducer with persistence
├── types/                  # TypeScript type definitions
│   └── auth.ts             # Authentication types
└── utils/                  # Utility functions
    └── token.ts            # Token handling utilities
```
## Authentication Flow

1. **Token Extraction**: The application extracts authentication tokens from URL parameters
2. **Token Validation**: Tokens are validated for authenticity and expiration
3. **User Data**: User profile information is extracted from the token payload
4. **State Management**: Authentication state is stored in Redux and persisted
5. **Protected Routes**: Routes under the `(protected)` directory require authentication
6. **API Requests**: Authenticated API requests include the token in the Authorization header
7. **Error Handling**: Authentication errors are handled gracefully with user feedback

## Deployment

The application can be deployed to any platform that supports Next.js applications, such as Vercel, Netlify, or a custom server.

```bash
# Build for production
npm run build

# Start production server
npm start
```

## License

MIT


## Available Scripts

- `npm run dev`: Start the development server
- `npm run build`: Build the application for production
- `npm start`: Start the production server
- `npm run lint`: Run ESLint to check code quality
