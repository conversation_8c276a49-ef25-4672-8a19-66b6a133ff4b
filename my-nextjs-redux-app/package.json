{"name": "my-nextjs-redux-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@reduxjs/toolkit": "^2.8.2", "@types/js-cookie": "^3.0.6", "@types/react-redux": "^7.1.34", "axios": "^1.11.0", "clsx": "^2.1.1", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lucide-react": "^0.536.0", "next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "tailwind-merge": "^3.3.1", "yup": "^1.7.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.9", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "madge": "^8.0.0", "prettier": "^3.6.2", "tailwindcss": "^4", "typescript": "^5.9.2"}}